"""
Advanced Position Mapping for PostgreSQL Error Resolution

This module provides comprehensive position mapping between PostgreSQL error messages
and split SQL statements, solving the core issue of line number misalignment.
"""

import re
import hashlib
from typing import Dict, List, Tuple, Optional, Any
from formatting.sql_splitter import split_sql_statements


class UniversalErrorExtractor:
    """Generic extractor that works with any PostgreSQL error format"""
    
    def extract_position_info(self, error_message: str) -> Dict[str, int]:
        """
        Extract any position/line information from error message
        
        Args:
            error_message: PostgreSQL error message
            
        Returns:
            Dict containing extracted position information
        """
        position_info = {}
        
        # All possible position patterns (generic)
        patterns = {
            'position': [
                r'Position:\s*(\d+)',
                r'position\s*(\d+)',
                r'pos:\s*(\d+)',
                r'at position\s*(\d+)',
                r'character\s*(\d+)'
            ],
            'line': [
                r'LINE\s*(\d+):',
                r'line:\s*(\d+)',
                r'at line\s*(\d+)',
                r'on line\s*(\d+)'
            ],
            'line_column': [
                r'line\s*(\d+),?\s*column\s*(\d+)',
                r'(\d+):(\d+)',
                r'line:\s*(\d+)\s+pos:\s*(\d+)'
            ]
        }
        
        # Try to extract any position information
        for info_type, pattern_list in patterns.items():
            for pattern in pattern_list:
                match = re.search(pattern, error_message, re.IGNORECASE)
                if match:
                    if info_type == 'line_column':
                        position_info['line'] = int(match.group(1))
                        position_info['column'] = int(match.group(2))
                    else:
                        position_info[info_type] = int(match.group(1))
                    break
            if info_type in position_info:
                break
        
        return position_info


class AdvancedPositionMapper:
    """
    Enhanced position mapper with comprehensive tracking of statement boundaries
    """

    def __init__(self):
        self.statement_ranges = {}  # statement_num -> (start_pos, end_pos, start_line, end_line)
        self.position_to_statement = {}  # char_pos -> statement_num
        self.line_to_statement = {}  # line_num -> statement_num
        self.statement_content_hash = {}  # content_hash -> [statement_nums]
        self.original_sql = ""
        self.lines = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'statement_ranges': self.statement_ranges,
            'position_to_statement': self.position_to_statement,
            'line_to_statement': self.line_to_statement,
            'statement_content_hash': self.statement_content_hash,
            'original_sql': self.original_sql,
            'lines': self.lines
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AdvancedPositionMapper':
        """Create from dictionary for deserialization"""
        mapper = cls()
        mapper.statement_ranges = data.get('statement_ranges', {})
        mapper.position_to_statement = data.get('position_to_statement', {})
        mapper.line_to_statement = data.get('line_to_statement', {})
        mapper.statement_content_hash = data.get('statement_content_hash', {})
        mapper.original_sql = data.get('original_sql', "")
        mapper.lines = data.get('lines', [])
        return mapper
    
    def split_with_comprehensive_mapping(self, sql_code: str) -> Tuple[List[str], 'AdvancedPositionMapper']:
        """
        Split SQL with comprehensive position tracking
        
        Args:
            sql_code: SQL code to split
            
        Returns:
            Tuple of (statements_list, position_mapper)
        """
        self.original_sql = sql_code
        self.lines = sql_code.split('\n')
        
        # Use existing split logic
        statements = split_sql_statements(sql_code)
        
        # Track positions for each statement
        self._map_statement_positions(sql_code, statements)
        
        return statements, self
    
    def _map_statement_positions(self, sql_code: str, statements: List[str]):
        """Map each statement to its position in the original SQL"""
        current_pos = 0
        current_line = 1
        
        for i, statement in enumerate(statements):
            statement_num = i + 1
            
            # Find the statement in the original SQL
            statement_start = sql_code.find(statement.strip(), current_pos)
            if statement_start == -1:
                # Fallback: use approximate position
                statement_start = current_pos
            
            statement_end = statement_start + len(statement.strip())
            
            # Calculate line numbers
            start_line = sql_code[:statement_start].count('\n') + 1
            end_line = sql_code[:statement_end].count('\n') + 1
            
            # Store comprehensive mapping
            self.statement_ranges[statement_num] = (
                statement_start, statement_end, start_line, end_line
            )
            
            # Create content hash for duplicate detection
            content_hash = self._create_content_hash(statement)
            if content_hash not in self.statement_content_hash:
                self.statement_content_hash[content_hash] = []
            self.statement_content_hash[content_hash].append(statement_num)
            
            # Map positions to statement
            for pos in range(statement_start, statement_end):
                self.position_to_statement[pos] = statement_num
            
            for line in range(start_line, end_line + 1):
                if line not in self.line_to_statement:
                    self.line_to_statement[line] = statement_num
            
            current_pos = statement_end
    
    def find_statement_by_any_position(self, position_info: Dict[str, int]) -> List[Tuple[int, str, int]]:
        """
        Return ALL possible statements that match the position
        
        Args:
            position_info: Dict with position/line information
            
        Returns:
            List of (statement_num, method, position) tuples
        """
        candidates = []
        
        if 'position' in position_info:
            stmt = self.position_to_statement.get(position_info['position'])
            if stmt:
                candidates.append((stmt, 'position', position_info['position']))
        
        if 'line' in position_info:
            stmt = self.line_to_statement.get(position_info['line'])
            if stmt:
                candidates.append((stmt, 'line', position_info['line']))
        
        # Remove duplicates but keep the method info
        unique_statements = {}
        for stmt_num, method, pos in candidates:
            if stmt_num not in unique_statements:
                unique_statements[stmt_num] = (method, pos)
        
        return [(stmt, method, pos) for stmt, (method, pos) in unique_statements.items()]
    
    def get_duplicate_statements(self, statement_num: int) -> List[int]:
        """Get all statements with similar content"""
        if statement_num not in self.statement_ranges:
            return []
        
        start_pos, end_pos, _, _ = self.statement_ranges[statement_num]
        statement_text = self.original_sql[start_pos:end_pos].strip()
        content_hash = self._create_content_hash(statement_text)
        
        return self.statement_content_hash.get(content_hash, [])
    
    def _create_content_hash(self, statement_text: str) -> str:
        """Create hash based on structural patterns, not hardcoded keywords"""
        # Normalize statement for comparison
        normalized = re.sub(r'\s+', ' ', statement_text.lower().strip())

        # Generic structural patterns (no hardcoded SQL keywords)
        structural_features = []

        # 1. Extract capitalized words (likely SQL keywords in any language)
        capitalized_words = re.findall(r'\b[A-Z]{2,}\b', statement_text)
        structural_features.extend([word.lower() for word in capitalized_words])

        # 2. Extract common SQL structural patterns
        patterns = [
            r'\b\w+\s+\w+\s+\w+\b',  # Three consecutive words (common in SQL)
            r'\(\s*[^)]+\s*\)',       # Parenthetical expressions
            r'\w+\s*=\s*\w+',        # Assignment/comparison patterns
            r'\w+\s*,\s*\w+',        # Comma-separated lists
            r'\w+\.\w+',             # Dot notation (table.column)
        ]

        for pattern in patterns:
            matches = re.findall(pattern, normalized)
            structural_features.extend([f"pattern_{len(match)}" for match in matches])

        # 3. Statement length and complexity indicators
        word_count = len(normalized.split())
        structural_features.append(f"words_{min(word_count//10*10, 100)}")  # Bucket word count

        # 4. Special character patterns
        special_chars = ['(', ')', ',', ';', '=', '<', '>', '!']
        for char in special_chars:
            count = normalized.count(char)
            if count > 0:
                structural_features.append(f"char_{char}_{min(count, 5)}")

        # Create hash from structural features
        feature_string = '|'.join(sorted(set(structural_features)))
        return hashlib.md5(feature_string.encode()).hexdigest()[:8]


class SmartStatementResolver:
    """
    Smart resolver that ranks candidate statements using multiple factors
    """

    def __init__(self, position_mapper: AdvancedPositionMapper):
        self.position_mapper = position_mapper
        self.previous_attempts = {}  # error_signature -> [attempted_statements]

    def resolve_with_context_ranking(self, error_message: str, statements: List[str],
                                   iteration_count: int = 1) -> Tuple[Optional[int], float, str]:
        """
        Resolve error to statement with context-aware ranking

        Args:
            error_message: PostgreSQL error message
            statements: List of SQL statements
            iteration_count: Current iteration number

        Returns:
            Tuple of (best_statement_num, confidence_score, resolution_method)
        """

        # Extract position information
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)

        if not position_info:
            return None, 0.0, 'no_position_info'

        # Get all candidate statements
        candidates = self.position_mapper.find_statement_by_any_position(position_info)

        if not candidates:
            return None, 0.0, 'no_candidates'

        # Create error signature for tracking attempts
        error_signature = self._create_error_signature(error_message)

        # Rank candidates based on multiple factors
        ranked_candidates = self._rank_candidates(
            candidates, error_message, statements, error_signature, iteration_count
        )

        if ranked_candidates:
            best_candidate = ranked_candidates[0]
            statement_num, confidence, method = best_candidate

            # Track this attempt
            if error_signature not in self.previous_attempts:
                self.previous_attempts[error_signature] = []
            self.previous_attempts[error_signature].append(statement_num)

            return statement_num, confidence, method

        return None, 0.0, 'no_ranked_candidates'

    def _rank_candidates(self, candidates: List[Tuple[int, str, int]], error_message: str,
                        statements: List[str], error_signature: str, iteration_count: int) -> List[Tuple[int, float, str]]:
        """Rank candidates based on multiple factors"""

        ranked = []
        previous_attempts = self.previous_attempts.get(error_signature, [])

        for stmt_num, method, position in candidates:
            confidence = 0.0

            # Factor 1: Position accuracy (higher for character position)
            # Give much higher base confidence for position-based resolution
            if method == 'position':
                confidence += 0.6  # Increased from 0.4
            elif method == 'line':
                confidence += 0.5  # Increased from 0.3

            # Factor 2: Content relevance (generic keyword matching)
            content_score = self._calculate_content_relevance(
                error_message, statements[stmt_num - 1]
            )
            confidence += content_score * 0.3

            # Factor 3: Previous attempt penalty (but don't exclude completely)
            attempt_penalty = previous_attempts.count(stmt_num) * 0.1
            confidence -= attempt_penalty

            # Factor 4: Iteration-based preference (try different statements in later iterations)
            if iteration_count > 1 and stmt_num in previous_attempts:
                # Get duplicate statements for this one
                duplicates = self.position_mapper.get_duplicate_statements(stmt_num)
                if len(duplicates) > 1:
                    # Prefer different occurrence in later iterations
                    occurrence_index = duplicates.index(stmt_num)
                    preferred_index = (iteration_count - 1) % len(duplicates)
                    if occurrence_index == preferred_index:
                        confidence += 0.2
                    else:
                        confidence -= 0.1

            # Factor 5: Statement complexity (more complex statements more likely to have errors)
            complexity_score = self._calculate_statement_complexity(statements[stmt_num - 1])
            confidence += complexity_score * 0.1

            ranked.append((stmt_num, max(0.0, confidence), f"{method}_ranked"))

        # Sort by confidence (descending)
        ranked.sort(key=lambda x: x[1], reverse=True)
        return ranked

    def _calculate_content_relevance(self, error_message: str, statement: str) -> float:
        """Completely generic content relevance using statistical similarity"""

        # Extract all meaningful tokens from error message
        error_tokens = self._extract_meaningful_tokens(error_message)
        statement_tokens = self._extract_meaningful_tokens(statement)

        if not error_tokens:
            return 0.5  # Neutral score if no tokens found

        # Calculate token overlap using Jaccard similarity
        error_set = set(error_tokens)
        statement_set = set(statement_tokens)

        intersection = len(error_set.intersection(statement_set))
        union = len(error_set.union(statement_set))

        if union == 0:
            return 0.0

        jaccard_similarity = intersection / union

        # Also consider token frequency similarity
        frequency_similarity = self._calculate_frequency_similarity(error_tokens, statement_tokens)

        # Combine both measures
        return (jaccard_similarity * 0.7) + (frequency_similarity * 0.3)

    def _extract_meaningful_tokens(self, text: str) -> List[str]:
        """Extract meaningful tokens from text without hardcoded keywords"""
        # Normalize text
        normalized = re.sub(r'\s+', ' ', text.lower().strip())

        # Extract different types of tokens
        tokens = []

        # 1. Quoted strings (high importance)
        quoted = re.findall(r'"([^"]+)"', text)
        tokens.extend([f"quoted_{q}" for q in quoted])

        # 2. Uppercase words (likely keywords)
        uppercase = re.findall(r'\b[A-Z]{2,}\b', text)
        tokens.extend([f"upper_{w.lower()}" for w in uppercase])

        # 3. Words longer than 3 characters (filter noise)
        words = re.findall(r'\b\w{3,}\b', normalized)
        tokens.extend([f"word_{w}" for w in words])

        # 4. Special patterns
        patterns = [
            (r'\b\w+\.\w+\b', 'dotted'),      # table.column
            (r'\b\w+\s*=\s*\w+\b', 'equals'), # assignments
            (r'\(\s*[^)]+\s*\)', 'parens'),   # parentheses
        ]

        for pattern, prefix in patterns:
            matches = re.findall(pattern, normalized)
            tokens.extend([f"{prefix}_{len(m)}" for m in matches])

        return tokens

    def _calculate_frequency_similarity(self, tokens1: List[str], tokens2: List[str]) -> float:
        """Calculate frequency-based similarity between token lists"""
        from collections import Counter

        if not tokens1 or not tokens2:
            return 0.0

        freq1 = Counter(tokens1)
        freq2 = Counter(tokens2)

        # Calculate cosine similarity of frequency vectors
        common_tokens = set(freq1.keys()).intersection(set(freq2.keys()))

        if not common_tokens:
            return 0.0

        dot_product = sum(freq1[token] * freq2[token] for token in common_tokens)
        magnitude1 = sum(freq ** 2 for freq in freq1.values()) ** 0.5
        magnitude2 = sum(freq ** 2 for freq in freq2.values()) ** 0.5

        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0

        return dot_product / (magnitude1 * magnitude2)

    def _calculate_statement_complexity(self, statement: str) -> float:
        """Calculate statement complexity using generic structural indicators"""

        complexity_score = 0.0

        # 1. Length-based complexity (longer statements are more complex)
        word_count = len(statement.split())
        length_score = min(1.0, word_count / 100.0)  # Normalize to 0-1
        complexity_score += length_score * 0.2

        # 2. Nesting level (parentheses depth)
        max_depth = 0
        current_depth = 0
        for char in statement:
            if char == '(':
                current_depth += 1
                max_depth = max(max_depth, current_depth)
            elif char == ')':
                current_depth -= 1
        nesting_score = min(1.0, max_depth / 5.0)  # Normalize to 0-1
        complexity_score += nesting_score * 0.3

        # 3. Special character density (more special chars = more complex)
        special_chars = [',', ';', '=', '<', '>', '!', '&', '|', '+', '-', '*', '/']
        special_count = sum(statement.count(char) for char in special_chars)
        special_density = min(1.0, special_count / len(statement) * 10)  # Normalize
        complexity_score += special_density * 0.2

        # 4. Uppercase word density (SQL keywords indicator)
        uppercase_words = len(re.findall(r'\b[A-Z]{2,}\b', statement))
        uppercase_density = min(1.0, uppercase_words / word_count if word_count > 0 else 0)
        complexity_score += uppercase_density * 0.2

        # 5. Line count (multi-line statements are more complex)
        line_count = len(statement.split('\n'))
        line_score = min(1.0, line_count / 10.0)  # Normalize to 0-1
        complexity_score += line_score * 0.1

        return min(1.0, complexity_score)

    def _create_error_signature(self, error_message: str) -> str:
        """Create a signature for the error to track attempts"""

        # Extract key parts of error message
        error_type = re.search(r'ERROR:\s*([^\n]+)', error_message)
        error_type_str = error_type.group(1) if error_type else 'unknown'

        # Create hash of error type + any quoted keywords
        quoted_words = re.findall(r'"([^"]+)"', error_message)
        signature_parts = [error_type_str] + quoted_words

        return '|'.join(signature_parts).lower()
