"""
Advanced Position Mapping for PostgreSQL Error Resolution

This module provides comprehensive position mapping between PostgreSQL error messages
and split SQL statements, solving the core issue of line number misalignment.
"""

import re
import hashlib
from typing import Dict, List, Tuple, Optional, Any
from formatting.sql_splitter import split_sql_statements


class UniversalErrorExtractor:
    """Generic extractor that works with any PostgreSQL error format"""
    
    def extract_position_info(self, error_message: str) -> Dict[str, int]:
        """
        Extract any position/line information from error message
        
        Args:
            error_message: PostgreSQL error message
            
        Returns:
            Dict containing extracted position information
        """
        position_info = {}
        
        # All possible position patterns (generic)
        patterns = {
            'position': [
                r'Position:\s*(\d+)',
                r'position\s*(\d+)',
                r'pos:\s*(\d+)',
                r'at position\s*(\d+)',
                r'character\s*(\d+)'
            ],
            'line': [
                r'LINE\s*(\d+):',
                r'line:\s*(\d+)',
                r'at line\s*(\d+)',
                r'on line\s*(\d+)'
            ],
            'line_column': [
                r'line\s*(\d+),?\s*column\s*(\d+)',
                r'(\d+):(\d+)',
                r'line:\s*(\d+)\s+pos:\s*(\d+)'
            ]
        }
        
        # Try to extract any position information
        for info_type, pattern_list in patterns.items():
            for pattern in pattern_list:
                match = re.search(pattern, error_message, re.IGNORECASE)
                if match:
                    if info_type == 'line_column':
                        position_info['line'] = int(match.group(1))
                        position_info['column'] = int(match.group(2))
                    else:
                        position_info[info_type] = int(match.group(1))
                    break
            if info_type in position_info:
                break
        
        return position_info


class AdvancedPositionMapper:
    """
    Enhanced position mapper with comprehensive tracking of statement boundaries
    """

    def __init__(self):
        self.statement_ranges = {}  # statement_num -> (start_pos, end_pos, start_line, end_line)
        self.position_to_statement = {}  # char_pos -> statement_num
        self.line_to_statement = {}  # line_num -> statement_num
        self.statement_content_hash = {}  # content_hash -> [statement_nums]
        self.original_sql = ""
        self.lines = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'statement_ranges': self.statement_ranges,
            'position_to_statement': self.position_to_statement,
            'line_to_statement': self.line_to_statement,
            'statement_content_hash': self.statement_content_hash,
            'original_sql': self.original_sql,
            'lines': self.lines
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AdvancedPositionMapper':
        """Create from dictionary for deserialization"""
        mapper = cls()
        mapper.statement_ranges = data.get('statement_ranges', {})
        mapper.position_to_statement = data.get('position_to_statement', {})
        mapper.line_to_statement = data.get('line_to_statement', {})
        mapper.statement_content_hash = data.get('statement_content_hash', {})
        mapper.original_sql = data.get('original_sql', "")
        mapper.lines = data.get('lines', [])
        return mapper
    
    def split_with_comprehensive_mapping(self, sql_code: str) -> Tuple[List[str], 'AdvancedPositionMapper']:
        """
        Split SQL with comprehensive position tracking
        
        Args:
            sql_code: SQL code to split
            
        Returns:
            Tuple of (statements_list, position_mapper)
        """
        self.original_sql = sql_code
        self.lines = sql_code.split('\n')
        
        # Use existing split logic
        statements = split_sql_statements(sql_code)
        
        # Track positions for each statement
        self._map_statement_positions(sql_code, statements)
        
        return statements, self
    
    def _map_statement_positions(self, sql_code: str, statements: List[str]):
        """Map each statement to its position in the original SQL"""
        current_pos = 0
        current_line = 1
        
        for i, statement in enumerate(statements):
            statement_num = i + 1
            
            # Find the statement in the original SQL
            statement_start = sql_code.find(statement.strip(), current_pos)
            if statement_start == -1:
                # Fallback: use approximate position
                statement_start = current_pos
            
            statement_end = statement_start + len(statement.strip())
            
            # Calculate line numbers
            start_line = sql_code[:statement_start].count('\n') + 1
            end_line = sql_code[:statement_end].count('\n') + 1
            
            # Store comprehensive mapping
            self.statement_ranges[statement_num] = (
                statement_start, statement_end, start_line, end_line
            )
            
            # Create content hash for duplicate detection
            content_hash = self._create_content_hash(statement)
            if content_hash not in self.statement_content_hash:
                self.statement_content_hash[content_hash] = []
            self.statement_content_hash[content_hash].append(statement_num)
            
            # Map positions to statement
            for pos in range(statement_start, statement_end):
                self.position_to_statement[pos] = statement_num
            
            for line in range(start_line, end_line + 1):
                if line not in self.line_to_statement:
                    self.line_to_statement[line] = statement_num
            
            current_pos = statement_end
    
    def find_statement_by_any_position(self, position_info: Dict[str, int]) -> List[Tuple[int, str, int]]:
        """
        Return ALL possible statements that match the position
        
        Args:
            position_info: Dict with position/line information
            
        Returns:
            List of (statement_num, method, position) tuples
        """
        candidates = []
        
        if 'position' in position_info:
            stmt = self.position_to_statement.get(position_info['position'])
            if stmt:
                candidates.append((stmt, 'position', position_info['position']))
        
        if 'line' in position_info:
            stmt = self.line_to_statement.get(position_info['line'])
            if stmt:
                candidates.append((stmt, 'line', position_info['line']))
        
        # Remove duplicates but keep the method info
        unique_statements = {}
        for stmt_num, method, pos in candidates:
            if stmt_num not in unique_statements:
                unique_statements[stmt_num] = (method, pos)
        
        return [(stmt, method, pos) for stmt, (method, pos) in unique_statements.items()]
    
    def get_duplicate_statements(self, statement_num: int) -> List[int]:
        """Get all statements with similar content"""
        if statement_num not in self.statement_ranges:
            return []
        
        start_pos, end_pos, _, _ = self.statement_ranges[statement_num]
        statement_text = self.original_sql[start_pos:end_pos].strip()
        content_hash = self._create_content_hash(statement_text)
        
        return self.statement_content_hash.get(content_hash, [])
    
    def _create_content_hash(self, statement_text: str) -> str:
        """Create hash based on significant keywords, not exact text"""
        # Normalize statement for comparison
        normalized = re.sub(r'\s+', ' ', statement_text.lower().strip())
        
        # Extract significant keywords
        keywords = []
        significant_patterns = [
            r'\b(select|insert|update|delete|create|alter|drop)\b',
            r'\b(from|where|group by|order by|having)\b',
            r'\b(inner join|left join|right join|full join)\b',
            r'\b(and|or|not|in|exists)\b'
        ]
        
        for pattern in significant_patterns:
            matches = re.findall(pattern, normalized)
            keywords.extend(matches)
        
        # Create hash from sorted keywords
        keyword_string = '|'.join(sorted(set(keywords)))
        return hashlib.md5(keyword_string.encode()).hexdigest()[:8]


class SmartStatementResolver:
    """
    Smart resolver that ranks candidate statements using multiple factors
    """

    def __init__(self, position_mapper: AdvancedPositionMapper):
        self.position_mapper = position_mapper
        self.previous_attempts = {}  # error_signature -> [attempted_statements]

    def resolve_with_context_ranking(self, error_message: str, statements: List[str],
                                   iteration_count: int = 1) -> Tuple[Optional[int], float, str]:
        """
        Resolve error to statement with context-aware ranking

        Args:
            error_message: PostgreSQL error message
            statements: List of SQL statements
            iteration_count: Current iteration number

        Returns:
            Tuple of (best_statement_num, confidence_score, resolution_method)
        """

        # Extract position information
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)

        if not position_info:
            return None, 0.0, 'no_position_info'

        # Get all candidate statements
        candidates = self.position_mapper.find_statement_by_any_position(position_info)

        if not candidates:
            return None, 0.0, 'no_candidates'

        # Create error signature for tracking attempts
        error_signature = self._create_error_signature(error_message)

        # Rank candidates based on multiple factors
        ranked_candidates = self._rank_candidates(
            candidates, error_message, statements, error_signature, iteration_count
        )

        if ranked_candidates:
            best_candidate = ranked_candidates[0]
            statement_num, confidence, method = best_candidate

            # Track this attempt
            if error_signature not in self.previous_attempts:
                self.previous_attempts[error_signature] = []
            self.previous_attempts[error_signature].append(statement_num)

            return statement_num, confidence, method

        return None, 0.0, 'no_ranked_candidates'

    def _rank_candidates(self, candidates: List[Tuple[int, str, int]], error_message: str,
                        statements: List[str], error_signature: str, iteration_count: int) -> List[Tuple[int, float, str]]:
        """Rank candidates based on multiple factors"""

        ranked = []
        previous_attempts = self.previous_attempts.get(error_signature, [])

        for stmt_num, method, position in candidates:
            confidence = 0.0

            # Factor 1: Position accuracy (higher for character position)
            if method == 'position':
                confidence += 0.4
            elif method == 'line':
                confidence += 0.3

            # Factor 2: Content relevance (generic keyword matching)
            content_score = self._calculate_content_relevance(
                error_message, statements[stmt_num - 1]
            )
            confidence += content_score * 0.3

            # Factor 3: Previous attempt penalty (but don't exclude completely)
            attempt_penalty = previous_attempts.count(stmt_num) * 0.1
            confidence -= attempt_penalty

            # Factor 4: Iteration-based preference (try different statements in later iterations)
            if iteration_count > 1 and stmt_num in previous_attempts:
                # Get duplicate statements for this one
                duplicates = self.position_mapper.get_duplicate_statements(stmt_num)
                if len(duplicates) > 1:
                    # Prefer different occurrence in later iterations
                    occurrence_index = duplicates.index(stmt_num)
                    preferred_index = (iteration_count - 1) % len(duplicates)
                    if occurrence_index == preferred_index:
                        confidence += 0.2
                    else:
                        confidence -= 0.1

            # Factor 5: Statement complexity (more complex statements more likely to have errors)
            complexity_score = self._calculate_statement_complexity(statements[stmt_num - 1])
            confidence += complexity_score * 0.1

            ranked.append((stmt_num, max(0.0, confidence), f"{method}_ranked"))

        # Sort by confidence (descending)
        ranked.sort(key=lambda x: x[1], reverse=True)
        return ranked

    def _calculate_content_relevance(self, error_message: str, statement: str) -> float:
        """Generic content relevance without specific patterns"""

        # Extract any quoted words from error message
        quoted_words = re.findall(r'"([^"]+)"', error_message)

        # Extract any uppercase words (likely SQL keywords)
        uppercase_words = re.findall(r'\b[A-Z]{2,}\b', error_message)

        # Combine all potential keywords
        keywords = quoted_words + uppercase_words

        if not keywords:
            return 0.5  # Neutral score if no keywords found

        # Check how many keywords appear in the statement
        statement_lower = statement.lower()
        matches = sum(1 for keyword in keywords if keyword.lower() in statement_lower)

        return min(1.0, matches / len(keywords))

    def _calculate_statement_complexity(self, statement: str) -> float:
        """Calculate statement complexity (more complex = more likely to have errors)"""

        complexity_indicators = [
            r'\bJOIN\b', r'\bUNION\b', r'\bSUBQUERY\b', r'\bCASE\b',
            r'\bGROUP BY\b', r'\bHAVING\b', r'\bWINDOW\b', r'\bCTE\b',
            r'\bEXISTS\b', r'\bNOT EXISTS\b'
        ]

        matches = sum(1 for pattern in complexity_indicators
                     if re.search(pattern, statement, re.IGNORECASE))

        # Normalize to 0-1 range
        return min(1.0, matches / 5.0)

    def _create_error_signature(self, error_message: str) -> str:
        """Create a signature for the error to track attempts"""

        # Extract key parts of error message
        error_type = re.search(r'ERROR:\s*([^\n]+)', error_message)
        error_type_str = error_type.group(1) if error_type else 'unknown'

        # Create hash of error type + any quoted keywords
        quoted_words = re.findall(r'"([^"]+)"', error_message)
        signature_parts = [error_type_str] + quoted_words

        return '|'.join(signature_parts).lower()
