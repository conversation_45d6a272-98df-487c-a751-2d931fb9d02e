"""
Position-based error mapping utilities for precise statement identification
"""

import re
from typing import List, Tuple, Optional, Dict, Any
from formatting.sql_splitter import split_sql_statements


class UniversalErrorExtractor:
    """Extract position information from any database error message"""
    
    def extract_position_info(self, error_message: str) -> Dict[str, int]:
        """Extract line or position information from error message"""
        position_info = {}
        
        # PostgreSQL line format: "LINE 139:"
        line_match = re.search(r'LINE\s+(\d+):', error_message, re.IGNORECASE)
        if line_match:
            position_info['line'] = int(line_match.group(1))
            return position_info
        
        # Position format: "Position: 17013" or "position 15420"
        pos_match = re.search(r'position[:\s]+(\d+)', error_message, re.IGNORECASE)
        if pos_match:
            position_info['position'] = int(pos_match.group(1))
            return position_info
        
        # Line format: "line: 447" or "line 184"
        line_match2 = re.search(r'line[:\s]+(\d+)', error_message, re.IGNORECASE)
        if line_match2:
            position_info['line'] = int(line_match2.group(1))
            return position_info
        
        return position_info


class AdvancedPositionMapper:
    """Advanced position mapping with comprehensive statement tracking"""
    
    def __init__(self):
        self.statement_ranges = []  # List of (start_pos, end_pos, start_line, end_line)
        self.position_to_statement = {}  # char_pos -> statement_number
        self.line_to_statement = {}  # line_number -> statement_number
        self.statement_content_hash = {}  # statement_number -> content_hash
        self.original_sql = ""
        self.lines = []
    
    def split_with_comprehensive_mapping(self, sql_code: str) -> Tuple[List[str], 'AdvancedPositionMapper']:
        """Split SQL and create comprehensive position mapping"""
        self.original_sql = sql_code
        self.lines = sql_code.split('\n')
        
        # Use the existing SQL splitter
        statements = split_sql_statements(sql_code)
        
        # Create position mapping
        self._create_position_mapping(sql_code, statements)
        
        return statements, self
    
    def _create_position_mapping(self, sql_code: str, statements: List[str]):
        """Create comprehensive position mapping"""
        current_pos = 0
        current_line = 1
        
        for stmt_num, statement in enumerate(statements, 1):
            # Find statement in original SQL
            stmt_start = sql_code.find(statement.strip(), current_pos)
            if stmt_start == -1:
                # Fallback: approximate position
                stmt_start = current_pos
            
            stmt_end = stmt_start + len(statement.strip())
            
            # Calculate line numbers
            lines_before = sql_code[:stmt_start].count('\n')
            lines_in_stmt = statement.count('\n')
            start_line = lines_before + 1
            end_line = start_line + lines_in_stmt
            
            # Store range
            self.statement_ranges.append((stmt_start, stmt_end, start_line, end_line))
            
            # Map positions to statement
            for pos in range(stmt_start, stmt_end + 1):
                self.position_to_statement[pos] = stmt_num
            
            # Map lines to statement
            for line in range(start_line, end_line + 1):
                self.line_to_statement[line] = stmt_num
            
            # Create content hash for duplicate detection
            content_hash = hash(statement.strip().lower())
            self.statement_content_hash[stmt_num] = content_hash
            
            current_pos = stmt_end
            current_line = end_line
    
    def find_statement_by_any_position(self, position_info: Dict[str, int]) -> List[Tuple[int, str, int]]:
        """Find statement by line or character position"""
        candidates = []
        
        if 'line' in position_info:
            line_num = position_info['line']
            if line_num in self.line_to_statement:
                stmt_num = self.line_to_statement[line_num]
                candidates.append((stmt_num, 'line', line_num))
        
        if 'position' in position_info:
            char_pos = position_info['position']
            if char_pos in self.position_to_statement:
                stmt_num = self.position_to_statement[char_pos]
                candidates.append((stmt_num, 'position', char_pos))
        
        return candidates
    
    def get_duplicate_statements(self, statement_num: int) -> List[int]:
        """Get all statements with same content hash (duplicates)"""
        if statement_num not in self.statement_content_hash:
            return [statement_num]
        
        target_hash = self.statement_content_hash[statement_num]
        duplicates = []
        
        for stmt_num, content_hash in self.statement_content_hash.items():
            if content_hash == target_hash:
                duplicates.append(stmt_num)
        
        return sorted(duplicates)
    
    def to_dict(self) -> Dict[str, Any]:
        """Serialize to dictionary"""
        return {
            'statement_ranges': self.statement_ranges,
            'position_to_statement': self.position_to_statement,
            'line_to_statement': self.line_to_statement,
            'statement_content_hash': self.statement_content_hash,
            'original_sql': self.original_sql,
            'lines': self.lines
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AdvancedPositionMapper':
        """Deserialize from dictionary"""
        mapper = cls()
        mapper.statement_ranges = data.get('statement_ranges', [])
        mapper.position_to_statement = data.get('position_to_statement', {})
        mapper.line_to_statement = data.get('line_to_statement', {})
        mapper.statement_content_hash = data.get('statement_content_hash', {})
        mapper.original_sql = data.get('original_sql', '')
        mapper.lines = data.get('lines', [])
        return mapper


class SmartStatementResolver:
    """Simple position-based statement resolver"""
    
    def __init__(self, position_mapper: AdvancedPositionMapper):
        self.position_mapper = position_mapper
    
    def resolve_statement_by_position(self, error_message: str, iteration_count: int = 1) -> Optional[int]:
        """Simple position-based statement resolution"""
        
        # Extract position information
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)
        
        if not position_info:
            print("📍 No position information found in error message")
            return None
        
        print(f"📍 Extracted position info: {position_info}")
        
        # Get candidate statements
        candidates = self.position_mapper.find_statement_by_any_position(position_info)
        
        if not candidates:
            print("📍 No statements found at error position")
            return None
        
        # Handle multiple candidates (duplicates)
        if len(candidates) == 1:
            statement_num = candidates[0][0]
            print(f"📍 Single statement found: #{statement_num}")
            return statement_num
        
        # Multiple candidates - use iteration to select different ones
        print(f"📍 Multiple candidates found: {[c[0] for c in candidates]}")
        
        # Get duplicates for the first candidate
        first_candidate = candidates[0][0]
        duplicates = self.position_mapper.get_duplicate_statements(first_candidate)
        
        if len(duplicates) > 1:
            # Rotate through duplicates based on iteration
            selected_index = (iteration_count - 1) % len(duplicates)
            selected_statement = duplicates[selected_index]
            print(f"📍 Selected statement #{selected_statement} (iteration {iteration_count}, duplicate {selected_index + 1}/{len(duplicates)})")
            return selected_statement
        
        # No duplicates, just return first candidate
        statement_num = candidates[0][0]
        print(f"📍 Using first candidate: #{statement_num}")
        return statement_num
