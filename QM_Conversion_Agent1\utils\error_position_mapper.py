"""
Advanced Position Mapping for PostgreSQL Error Resolution

This module provides comprehensive position mapping between PostgreSQL error messages
and split SQL statements, solving the core issue of line number misalignment.
"""

import re
import hashlib
from typing import Dict, List, Tuple, Optional, Any
from formatting.sql_splitter import split_sql_statements


class UniversalErrorExtractor:
    """Generic extractor that works with any PostgreSQL error format"""
    
    def extract_position_info(self, error_message: str) -> Dict[str, int]:
        """
        Extract any position/line information from error message
        
        Args:
            error_message: PostgreSQL error message
            
        Returns:
            Dict containing extracted position information
        """
        position_info = {}
        
        # All possible position patterns (generic)
        patterns = {
            'position': [
                r'Position:\s*(\d+)',
                r'position\s*(\d+)',
                r'pos:\s*(\d+)',
                r'at position\s*(\d+)',
                r'character\s*(\d+)'
            ],
            'line': [
                r'LINE\s*(\d+):',
                r'line:\s*(\d+)',
                r'at line\s*(\d+)',
                r'on line\s*(\d+)'
            ],
            'line_column': [
                r'line\s*(\d+),?\s*column\s*(\d+)',
                r'(\d+):(\d+)',
                r'line:\s*(\d+)\s+pos:\s*(\d+)'
            ]
        }
        
        # Try to extract any position information
        for info_type, pattern_list in patterns.items():
            for pattern in pattern_list:
                match = re.search(pattern, error_message, re.IGNORECASE)
                if match:
                    if info_type == 'line_column':
                        position_info['line'] = int(match.group(1))
                        position_info['column'] = int(match.group(2))
                    else:
                        position_info[info_type] = int(match.group(1))
                    break
            if info_type in position_info:
                break
        
        return position_info


class AdvancedPositionMapper:
    """
    Enhanced position mapper with comprehensive tracking of statement boundaries
    """

    def __init__(self):
        self.statement_ranges = {}  # statement_num -> (start_pos, end_pos, start_line, end_line)
        self.position_to_statement = {}  # char_pos -> statement_num
        self.line_to_statement = {}  # line_num -> statement_num
        self.statement_content_hash = {}  # content_hash -> [statement_nums]
        self.original_sql = ""
        self.lines = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'statement_ranges': self.statement_ranges,
            'position_to_statement': self.position_to_statement,
            'line_to_statement': self.line_to_statement,
            'statement_content_hash': self.statement_content_hash,
            'original_sql': self.original_sql,
            'lines': self.lines
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AdvancedPositionMapper':
        """Create from dictionary for deserialization"""
        mapper = cls()
        mapper.statement_ranges = data.get('statement_ranges', {})
        mapper.position_to_statement = data.get('position_to_statement', {})
        mapper.line_to_statement = data.get('line_to_statement', {})
        mapper.statement_content_hash = data.get('statement_content_hash', {})
        mapper.original_sql = data.get('original_sql', "")
        mapper.lines = data.get('lines', [])
        return mapper
    
    def split_with_comprehensive_mapping(self, sql_code: str) -> Tuple[List[str], 'AdvancedPositionMapper']:
        """
        Split SQL with comprehensive position tracking
        
        Args:
            sql_code: SQL code to split
            
        Returns:
            Tuple of (statements_list, position_mapper)
        """
        self.original_sql = sql_code
        self.lines = sql_code.split('\n')
        
        # Use existing split logic
        statements = split_sql_statements(sql_code)
        
        # Track positions for each statement
        self._map_statement_positions(sql_code, statements)
        
        return statements, self
    
    def _map_statement_positions(self, sql_code: str, statements: List[str]):
        """Map each statement to its position in the original SQL"""
        current_pos = 0
        current_line = 1
        
        for i, statement in enumerate(statements):
            statement_num = i + 1
            
            # Find the statement in the original SQL
            statement_start = sql_code.find(statement.strip(), current_pos)
            if statement_start == -1:
                # Fallback: use approximate position
                statement_start = current_pos
            
            statement_end = statement_start + len(statement.strip())
            
            # Calculate line numbers
            start_line = sql_code[:statement_start].count('\n') + 1
            end_line = sql_code[:statement_end].count('\n') + 1
            
            # Store comprehensive mapping
            self.statement_ranges[statement_num] = (
                statement_start, statement_end, start_line, end_line
            )
            
            # Create content hash for duplicate detection
            content_hash = self._create_content_hash(statement)
            if content_hash not in self.statement_content_hash:
                self.statement_content_hash[content_hash] = []
            self.statement_content_hash[content_hash].append(statement_num)
            
            # Map positions to statement
            for pos in range(statement_start, statement_end):
                self.position_to_statement[pos] = statement_num
            
            for line in range(start_line, end_line + 1):
                if line not in self.line_to_statement:
                    self.line_to_statement[line] = statement_num
            
            current_pos = statement_end
    
    def find_statement_by_any_position(self, position_info: Dict[str, int]) -> List[Tuple[int, str, int]]:
        """
        Return ALL possible statements that match the position
        
        Args:
            position_info: Dict with position/line information
            
        Returns:
            List of (statement_num, method, position) tuples
        """
        candidates = []
        
        if 'position' in position_info:
            stmt = self.position_to_statement.get(position_info['position'])
            if stmt:
                candidates.append((stmt, 'position', position_info['position']))
        
        if 'line' in position_info:
            stmt = self.line_to_statement.get(position_info['line'])
            if stmt:
                candidates.append((stmt, 'line', position_info['line']))
        
        # Remove duplicates but keep the method info
        unique_statements = {}
        for stmt_num, method, pos in candidates:
            if stmt_num not in unique_statements:
                unique_statements[stmt_num] = (method, pos)
        
        return [(stmt, method, pos) for stmt, (method, pos) in unique_statements.items()]
    
    def get_duplicate_statements(self, statement_num: int) -> List[int]:
        """Get all statements with similar content"""
        if statement_num not in self.statement_ranges:
            return []
        
        start_pos, end_pos, _, _ = self.statement_ranges[statement_num]
        statement_text = self.original_sql[start_pos:end_pos].strip()
        content_hash = self._create_content_hash(statement_text)
        
        return self.statement_content_hash.get(content_hash, [])
    
    def _create_content_hash(self, statement_text: str) -> str:
        """Create hash based on structural patterns, not hardcoded keywords"""
        # Normalize statement for comparison
        normalized = re.sub(r'\s+', ' ', statement_text.lower().strip())

        # Generic structural patterns (no hardcoded SQL keywords)
        structural_features = []

        # 1. Extract capitalized words (likely SQL keywords in any language)
        capitalized_words = re.findall(r'\b[A-Z]{2,}\b', statement_text)
        structural_features.extend([word.lower() for word in capitalized_words])

        # 2. Extract common SQL structural patterns
        patterns = [
            r'\b\w+\s+\w+\s+\w+\b',  # Three consecutive words (common in SQL)
            r'\(\s*[^)]+\s*\)',       # Parenthetical expressions
            r'\w+\s*=\s*\w+',        # Assignment/comparison patterns
            r'\w+\s*,\s*\w+',        # Comma-separated lists
            r'\w+\.\w+',             # Dot notation (table.column)
        ]

        for pattern in patterns:
            matches = re.findall(pattern, normalized)
            structural_features.extend([f"pattern_{len(match)}" for match in matches])

        # 3. Statement length and complexity indicators
        word_count = len(normalized.split())
        structural_features.append(f"words_{min(word_count//10*10, 100)}")  # Bucket word count

        # 4. Special character patterns
        special_chars = ['(', ')', ',', ';', '=', '<', '>', '!']
        for char in special_chars:
            count = normalized.count(char)
            if count > 0:
                structural_features.append(f"char_{char}_{min(count, 5)}")

        # Create hash from structural features
        feature_string = '|'.join(sorted(set(structural_features)))
        return hashlib.md5(feature_string.encode()).hexdigest()[:8]


class SmartStatementResolver:
    """
    Smart resolver that ranks candidate statements using multiple factors
    """

    def __init__(self, position_mapper: AdvancedPositionMapper):
        self.position_mapper = position_mapper
        self.previous_attempts = {}  # error_signature -> [attempted_statements]

    def resolve_statement_by_position(self, error_message: str, iteration_count: int = 1) -> Optional[int]:
        """
        Simple position-based statement resolution without confidence/ranking

        Args:
            error_message: PostgreSQL error message
            iteration_count: Current iteration number for handling duplicates

        Returns:
            Statement number or None
        """

        # Extract position information
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)

        if not position_info:
            print("📍 No position information found in error message")
            return None

        print(f"📍 Extracted position info: {position_info}")

        # Get candidate statements
        candidates = self.position_mapper.find_statement_by_any_position(position_info)

        if not candidates:
            print("📍 No statements found at error position")
            return None

        # Handle multiple candidates (duplicates)
        if len(candidates) == 1:
            statement_num = candidates[0][0]
            print(f"📍 Single statement found: #{statement_num}")
            return statement_num

        # Multiple candidates - use iteration to select different ones
        print(f"📍 Multiple candidates found: {[c[0] for c in candidates]}")

        # Simple approach - no need for error signature tracking

        # Get duplicates for the first candidate
        first_candidate = candidates[0][0]
        duplicates = self.position_mapper.get_duplicate_statements(first_candidate)

        if len(duplicates) > 1:
            # Rotate through duplicates based on iteration
            selected_index = (iteration_count - 1) % len(duplicates)
            selected_statement = duplicates[selected_index]
            print(f"📍 Selected statement #{selected_statement} (iteration {iteration_count}, duplicate {selected_index + 1}/{len(duplicates)})")
            return selected_statement

        # No duplicates, just return first candidate
        statement_num = candidates[0][0]
        print(f"📍 Using first candidate: #{statement_num}")
        return statement_num

    def _create_error_signature(self, error_message: str) -> str:
        """Create a simple signature for the error"""
        # Extract key parts of error message for duplicate tracking
        error_type = re.search(r'ERROR:\s*([^\n]+)', error_message)
        error_type_str = error_type.group(1) if error_type else 'unknown'
        quoted_words = re.findall(r'"([^"]+)"', error_message)
        signature_parts = [error_type_str] + quoted_words
        return '|'.join(signature_parts).lower()


