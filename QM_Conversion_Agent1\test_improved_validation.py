#!/usr/bin/env python3
"""
Test script for improved position-based validation
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.error_position_mapper import (
    UniversalErrorExtractor,
    AdvancedPositionMapper,
    SmartStatementResolver
)

def test_improved_confidence_scores():
    """Test that position-based resolution gets higher confidence scores"""
    print("🧪 Testing Improved Confidence Scores...")
    
    # Sample SQL with GROUP BY error
    sample_sql = """SET search_path TO LAB;

CREATE OR REPLACE PROCEDURE lab.P_TEST ()
LANGUAGE plpgsql
AS $BODY$
BEGIN
    SELECT COUNT(*) FROM table1;
    
    INSERT INTO table2 (col1, col2)
    SELECT col1, col2 
    FROM table3
    WHERE condition = 'value'
    GROUP BY col1, col2
    HAVING MAX(eta.test_transmission_time) = MAX(eta.test_transmission_time);
    
    UPDATE table4 
    SET status = 'updated'
    WHERE id > 100;
END;
$BODY$;"""
    
    # Error message similar to your actual error
    error_message = 'syntax error at or near "GROUP"\nLINE 13:'
    
    # Create mapper and resolver
    mapper = AdvancedPositionMapper()
    statements, position_mapper = mapper.split_with_comprehensive_mapping(sample_sql)
    
    print(f"📊 Split into {len(statements)} statements")
    
    # Test position extraction
    extractor = UniversalErrorExtractor()
    position_info = extractor.extract_position_info(error_message)
    print(f"📍 Extracted position info: {position_info}")
    
    # Test resolution with improved confidence
    resolver = SmartStatementResolver(position_mapper)
    
    for iteration in [1, 2, 3]:
        print(f"\n🔄 Iteration {iteration}:")
        statement_num, confidence, method = resolver.resolve_with_context_ranking(
            error_message, statements, iteration_count=iteration
        )
        
        if statement_num:
            print(f"   🎯 Resolved to statement #{statement_num}")
            print(f"   📊 Confidence: {confidence:.3f} (method: {method})")
            print(f"   📝 Statement: {statements[statement_num-1][:100]}...")
            
            # Check confidence level
            if confidence > 0.5:
                print(f"   🚀 HIGH confidence - would skip validation")
            elif confidence > 0.2:
                print(f"   🔍 MEDIUM confidence - would use lenient validation")
            else:
                print(f"   ⚠️ LOW confidence - would use strict validation")
        else:
            print(f"   ❌ No statement resolved")
    
    print("✅ Improved confidence scoring tests completed")

def test_validation_factors():
    """Test the multi-factor validation approach"""
    print("\n🧪 Testing Multi-Factor Validation...")
    
    # Test cases with different types of statements
    test_cases = [
        {
            'error': 'syntax error at or near "GROUP"\nLINE 5:',
            'statement': 'SELECT col1, COUNT(*) FROM table GROUP BY col1 HAVING MAX(time) = MAX(time);',
            'expected': 'Should pass validation (has GROUP, reasonable complexity)'
        },
        {
            'error': 'syntax error at or near "INSERT"\nLINE 10:',
            'statement': 'INSERT INTO table (col1, col2) VALUES (1, 2);',
            'expected': 'Should pass validation (has INSERT, some complexity)'
        },
        {
            'error': 'undefined function "UNKNOWN_FUNC"\nPosition: 150',
            'statement': 'SELECT UNKNOWN_FUNC(col1) FROM table;',
            'expected': 'Should pass validation (has UNKNOWN_FUNC token)'
        },
        {
            'error': 'syntax error at or near "WHERE"\nLINE 8:',
            'statement': 'SELECT col1 FROM table1;',
            'expected': 'Might fail validation (no WHERE, low complexity)'
        }
    ]
    
    # Mock error context class
    class MockErrorContext:
        def __init__(self, statement):
            self.error_statement = statement
    
    # Import the validation function
    from nodes.conversion_nodes import ConversionNodes
    converter = ConversionNodes()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test case {i}: {test_case['expected']}")
        print(f"   Error: {test_case['error'][:50]}...")
        print(f"   Statement: {test_case['statement'][:50]}...")
        
        error_context = MockErrorContext(test_case['statement'])
        is_valid = converter.validate_position_based_resolution(error_context, test_case['error'])
        
        print(f"   Result: {'✅ PASSED' if is_valid else '❌ FAILED'} validation")
    
    print("✅ Multi-factor validation tests completed")

def test_confidence_thresholds():
    """Test different confidence threshold behaviors"""
    print("\n🧪 Testing Confidence Threshold Behaviors...")
    
    # Mock scenarios with different confidence levels
    confidence_scenarios = [
        (0.7, "High confidence - should skip validation"),
        (0.4, "Medium confidence - should use lenient validation"),
        (0.15, "Low confidence - should use strict validation"),
        (0.05, "Very low confidence - likely to fail")
    ]
    
    for confidence, description in confidence_scenarios:
        print(f"\n📊 Testing confidence {confidence:.2f}: {description}")
        
        if confidence > 0.5:
            print(f"   🚀 Would skip validation entirely")
        elif confidence > 0.2:
            print(f"   🔍 Would use lenient validation criteria")
        else:
            print(f"   ⚠️ Would require strict validation")
    
    print("✅ Confidence threshold tests completed")

def main():
    """Run all improved validation tests"""
    print("🚀 Starting Improved Validation Tests...\n")
    
    try:
        test_improved_confidence_scores()
        test_validation_factors()
        test_confidence_thresholds()
        
        print("\n🎉 All improved validation tests completed!")
        print("\n📋 Summary of Improvements:")
        print("   ✅ Higher base confidence for position-based resolution")
        print("   ✅ Multi-factor validation approach")
        print("   ✅ Confidence-based validation strategy")
        print("   ✅ Lenient criteria for precise position mapping")
        print("   ✅ Trust position mapping over statistical similarity")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
