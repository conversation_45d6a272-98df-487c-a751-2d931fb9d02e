#!/usr/bin/env python3
"""
Test script for AI-only approach with iteration awareness
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ai_only_approach():
    """Test the AI-only approach with different iterations"""
    print("🧪 Testing AI-Only Approach with Iteration Awareness...")
    
    # Sample statements with multiple GROUP BY occurrences
    sample_statements = [
        "SET search_path TO LAB;",
        "CREATE OR REPLACE PROCEDURE test();",
        "SELECT col1 FROM table1;",
        "INSERT INTO table2 VALUES (1, 2);",
        "SELECT col1, COUNT(*) FROM table3 GROUP BY col1;",  # Statement 5 - First GROUP BY
        "UPDATE table4 SET status = 'done';",
        "SELECT col2, SUM(amount) FROM table5 GROUP BY col2;",  # Statement 7 - Second GROUP BY  
        "DELETE FROM table6 WHERE id = 1;",
        "SELECT col3, AVG(value) FROM table7 GROUP BY col3 HAVING COUNT(*) > 5;",  # Statement 9 - Third GROUP BY
        "COMMIT;"
    ]
    
    # Error message pointing to GROUP BY
    error_message = 'syntax error at or near "GROUP"\nLINE 184:'
    
    print(f"📊 Testing with {len(sample_statements)} statements")
    print(f"📊 Error: {error_message}")
    print(f"📊 Expected behavior:")
    print(f"   - Iteration 1: Should select statement #5 (first GROUP BY)")
    print(f"   - Iteration 2: Should select statement #7 (second GROUP BY)")  
    print(f"   - Iteration 3: Should select statement #9 (third GROUP BY)")
    
    # Test different iterations
    for iteration in [1, 2, 3]:
        print(f"\n🔄 Testing Iteration {iteration}:")
        
        # Create iteration context
        context_parts = []
        
        if iteration > 1:
            context_parts.append(f"This is iteration {iteration} of error resolution.")
            context_parts.append("Previous iteration(s) failed to resolve the error.")
            
            if iteration == 2:
                context_parts.append("If there are multiple similar statements (e.g., multiple GROUP BY, INSERT, SELECT statements), try a DIFFERENT occurrence than the first one.")
            elif iteration == 3:
                context_parts.append("If there are multiple similar statements, try the THIRD occurrence or a different variation.")
        
        iteration_context = "ITERATION CONTEXT:\n" + "\n".join(context_parts) + "\n\nBased on this context, " if context_parts else ""
        
        print(f"   📝 Iteration context: {iteration_context[:100]}..." if iteration_context else "   📝 No iteration context (first iteration)")
        
        # Simulate AI logic for finding GROUP BY statements
        group_by_statements = []
        for i, stmt in enumerate(sample_statements, 1):
            if "GROUP BY" in stmt.upper():
                group_by_statements.append((i, stmt))
        
        print(f"   📊 Found {len(group_by_statements)} GROUP BY statements: {[num for num, _ in group_by_statements]}")
        
        # Select based on iteration
        if iteration <= len(group_by_statements):
            selected_num, selected_stmt = group_by_statements[iteration - 1]
            print(f"   🎯 Selected statement #{selected_num}: {selected_stmt[:50]}...")
        else:
            print(f"   ⚠️ No more GROUP BY statements available for iteration {iteration}")
    
    print("\n✅ AI-only approach iteration test completed")

def test_ai_prompt_enhancement():
    """Test how to enhance AI prompts for iteration awareness"""
    print("\n🧪 Testing AI Prompt Enhancement...")
    
    base_prompt = """
    Analyze the following PostgreSQL error and identify which statement is causing it:
    
    Error: syntax error at or near "GROUP"
    LINE 184:
    
    Statements:
    1. SELECT col1 FROM table1;
    2. SELECT col1, COUNT(*) FROM table3 GROUP BY col1;
    3. UPDATE table4 SET status = 'done';
    4. SELECT col2, SUM(amount) FROM table5 GROUP BY col2;
    5. SELECT col3, AVG(value) FROM table7 GROUP BY col3 HAVING COUNT(*) > 5;
    """
    
    iteration_enhancements = {
        1: "",  # No enhancement for first iteration
        2: """
        ITERATION CONTEXT:
        This is iteration 2 of error resolution.
        Previous iteration(s) failed to resolve the error.
        If there are multiple similar statements (e.g., multiple GROUP BY, INSERT, SELECT statements), try a DIFFERENT occurrence than the first one.
        
        Based on this context, """,
        3: """
        ITERATION CONTEXT:
        This is iteration 3 of error resolution.
        Previous iteration(s) failed to resolve the error.
        If there are multiple similar statements, try the THIRD occurrence or a different variation.
        
        Based on this context, """
    }
    
    for iteration, enhancement in iteration_enhancements.items():
        print(f"\n📝 Iteration {iteration} prompt:")
        enhanced_prompt = enhancement + base_prompt if enhancement else base_prompt
        print(f"   Length: {len(enhanced_prompt)} characters")
        print(f"   Contains iteration guidance: {'Yes' if 'ITERATION CONTEXT' in enhanced_prompt else 'No'}")
        if enhancement:
            print(f"   Key instruction: {enhancement.split('Based on this context,')[0].strip().split('.')[-2]}.")
    
    print("\n✅ AI prompt enhancement test completed")

def main():
    """Run all AI-only approach tests"""
    print("🚀 Starting AI-Only Approach Tests...\n")
    
    try:
        test_ai_only_approach()
        test_ai_prompt_enhancement()
        
        print("\n🎉 All AI-only approach tests completed!")
        print("\n📋 Summary:")
        print("   ✅ AI can handle multiple occurrences with iteration context")
        print("   ✅ Iteration-aware prompts provide clear guidance")
        print("   ✅ No position mapping dependency required")
        print("   ✅ Simple, maintainable approach")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
