# PostgreSQL Error Position Mapping Implementation Guide

## Overview

This implementation solves the core issue where PostgreSQL error line numbers don't match split statement numbers, causing the AI to repeatedly identify incorrect statements.

## Problem Solved

**Before**: 
- PostgreSQL reports error at LINE 184
- AI analyzes split statements and always picks statement #30 (first occurrence)
- Same error repeats across iterations

**After**:
- PostgreSQL error at LINE 184 maps directly to the correct split statement
- AI gets different statement occurrences in different iterations
- Precise position-based error resolution

## Key Components

### 1. UniversalErrorExtractor
```python
from utils.error_position_mapper import UniversalErrorExtractor

extractor = UniversalErrorExtractor()
position_info = extractor.extract_position_info(error_message)
```

**Handles all PostgreSQL error formats:**
- `Position: 17013`
- `LINE 184:`
- `line: 447 pos: 17012`
- `at position 15420`
- `error at line 184`

### 2. AdvancedPositionMapper
```python
from utils.error_position_mapper import AdvancedPositionMapper

mapper = AdvancedPositionMapper()
statements, position_mapper = mapper.split_with_comprehensive_mapping(sql_code)
```

**Creates bidirectional mapping:**
- Character position → Statement number
- Line number → Statement number
- Statement number → Position range
- Content hash → Duplicate statements

### 3. SmartStatementResolver
```python
from utils.error_position_mapper import SmartStatementResolver

resolver = SmartStatementResolver(position_mapper)
statement_num, confidence, method = resolver.resolve_with_context_ranking(
    error_message, statements, iteration_count
)
```

**Intelligent ranking based on:**
- Position accuracy (40% weight)
- Content relevance (30% weight)
- Previous attempts penalty (10% weight)
- Iteration-based preference (20% weight)
- Statement complexity (10% weight)

## Integration Points

### 1. Enhanced splitStatments() Method

**Before:**
```python
source_statements = split_sql_statements(source_code)
target_statements = split_sql_statements(target_code)
```

**After:**
```python
# Create position mappers
source_mapper = AdvancedPositionMapper()
target_mapper = AdvancedPositionMapper()

# Split with position tracking
source_statements, source_position_mapper = source_mapper.split_with_comprehensive_mapping(source_code)
target_statements, target_position_mapper = target_mapper.split_with_comprehensive_mapping(target_code)

# Store in workflow state
return {
    "source_statements": source_statements,
    "target_statements": target_statements,
    "source_position_mapper": source_position_mapper,
    "target_position_mapper": target_position_mapper,
    "iteration_count": iteration_count
}
```

### 2. Enhanced AnalyzeError_identifyTargetStatements() Method

**New workflow:**
1. **Try position-based resolution first**
2. **Validate the resolution**
3. **Fallback to AI if needed**

```python
# Get position mapper from state
target_position_mapper = getattr(state, 'target_position_mapper', None)

if target_position_mapper:
    # Smart position-based resolution
    resolver = SmartStatementResolver(target_position_mapper)
    statement_num, confidence, method = resolver.resolve_with_context_ranking(
        deployment_error, target_statements, iteration_count
    )
    
    if statement_num and confidence > 0.3:
        # Create error context
        target_error_context = self.create_error_context_around_statement(
            target_statements, statement_num
        )
        
        # Validate resolution
        if self.validate_position_based_resolution(target_error_context, deployment_error):
            # Use position-based result
            return {"target_error_context": target_error_context}

# Fallback to AI approach
target_error_context = self.findErrorStatementWithTwoPhaseAI(...)
```

## Multiple Occurrences Handling

### Problem
AI always selects first occurrence of similar statements (e.g., multiple GROUP BY statements).

### Solution
**Iteration-based rotation:**
```python
# If we have duplicate statements [stmt_5, stmt_12, stmt_28]
# Iteration 1: Try stmt_5
# Iteration 2: Try stmt_12  
# Iteration 3: Try stmt_28
```

**Implementation:**
```python
if iteration_count > 1 and stmt_num in previous_attempts:
    duplicates = position_mapper.get_duplicate_statements(stmt_num)
    if len(duplicates) > 1:
        occurrence_index = duplicates.index(stmt_num)
        preferred_index = (iteration_count - 1) % len(duplicates)
        if occurrence_index == preferred_index:
            confidence += 0.2  # Boost confidence for preferred occurrence
```

## Error Signature Tracking

**Prevents cross-contamination between different error types:**
```python
def _create_error_signature(self, error_message: str) -> str:
    error_type = re.search(r'ERROR:\s*([^\n]+)', error_message)
    quoted_words = re.findall(r'"([^"]+)"', error_message)
    signature_parts = [error_type_str] + quoted_words
    return '|'.join(signature_parts).lower()
```

**Examples:**
- `syntax_error_near_group`
- `column_must_appear_in_group_by`
- `function_does_not_exist`

## Validation Logic

### Position-Based Validation
```python
def validate_position_based_resolution(self, error_context, error_message):
    # Extract keywords from error message
    quoted_keywords = re.findall(r'"([^"]+)"', error_message)
    uppercase_keywords = re.findall(r'\b[A-Z]{2,}\b', error_message)
    
    # Check if keywords exist in resolved statement
    error_statement_lower = error_context.error_statement.lower()
    for keyword in all_keywords:
        if keyword.lower() in error_statement_lower:
            return True
    return False
```

## Benefits

### ✅ Precise Error Resolution
- **Character-level accuracy** for Position-based errors
- **Direct mapping** from PostgreSQL errors to statements

### ✅ Handles Multiple Occurrences
- **Systematic rotation** through similar statements
- **No more stuck on first occurrence**

### ✅ Generic Approach
- **Works with any PostgreSQL error format**
- **No hardcoded error patterns**

### ✅ Intelligent Fallback
- **Position-based resolution first**
- **AI-based fallback when needed**
- **Confidence-based decision making**

### ✅ Source Mapping Compatible
- **Same approach works for source mapping**
- **Consistent methodology across workflow**

## Testing

Run the test script to verify functionality:
```bash
cd QM_Conversion_Agent1
python test_position_mapping.py
```

## Monitoring

The system provides detailed logging:
```
🎯 Smart resolution: Statement #30 (confidence: 0.85, method: position_ranked)
✅ Position-based resolution validated
📍 Created target position mapping for error resolution
```

## Configuration

**Confidence threshold** (adjustable):
```python
if statement_num and confidence > 0.3:  # 30% confidence threshold
```

**Factor weights** (customizable):
- Position accuracy: 40%
- Content relevance: 30% 
- Iteration preference: 20%
- Statement complexity: 10%
- Previous attempts penalty: 10%
