#!/usr/bin/env python3
"""
Test script demonstrating the completely generic approach
without hardcoded SQL keywords
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.error_position_mapper import (
    UniversalErrorExtractor,
    AdvancedPositionMapper,
    SmartStatementResolver
)

def test_generic_content_hash():
    """Test generic content hashing without SQL keywords"""
    print("🧪 Testing Generic Content Hashing...")
    
    mapper = AdvancedPositionMapper()
    
    # Test with different types of statements (not just SQL)
    test_statements = [
        "SELECT col1, col2 FROM table1 WHERE condition = 'value'",
        "INSERT INTO table2 (col1, col2) VALUES (1, 'test')",
        "UPDATE table3 SET status = 'done' WHERE id > 100",
        "CREATE PROCEDURE test_proc() AS BEGIN SELECT * FROM table; END",
        "DECLARE @var INT = 10; SELECT @var AS result;",  # SQL Server syntax
        "def function_name(param1, param2): return param1 + param2",  # Python-like
        "for (int i = 0; i < 10; i++) { print(i); }",  # C-like
    ]
    
    print("\n📊 Content hashes (based on structure, not keywords):")
    for i, stmt in enumerate(test_statements, 1):
        content_hash = mapper._create_content_hash(stmt)
        print(f"   {i}. Hash: {content_hash} | Statement: {stmt[:50]}...")
    
    # Test duplicate detection
    similar_statements = [
        "SELECT col1, col2 FROM table1 WHERE condition = 'A'",
        "SELECT col3, col4 FROM table2 WHERE condition = 'B'",
        "INSERT INTO table1 (col1, col2) VALUES (1, 'X')",
        "INSERT INTO table2 (col3, col4) VALUES (2, 'Y')",
    ]
    
    print("\n🔍 Testing duplicate detection:")
    hashes = {}
    for stmt in similar_statements:
        content_hash = mapper._create_content_hash(stmt)
        if content_hash not in hashes:
            hashes[content_hash] = []
        hashes[content_hash].append(stmt[:30] + "...")
    
    for hash_val, statements in hashes.items():
        if len(statements) > 1:
            print(f"   📎 Similar statements (hash: {hash_val}):")
            for stmt in statements:
                print(f"      - {stmt}")
    
    print("✅ Generic content hashing tests completed")

def test_generic_content_relevance():
    """Test generic content relevance calculation"""
    print("\n🧪 Testing Generic Content Relevance...")
    
    mapper = AdvancedPositionMapper()
    resolver = SmartStatementResolver(mapper)
    
    # Test with various error types and languages
    test_cases = [
        {
            'error': 'syntax error at or near "GROUP"\nLINE 5:',
            'statements': [
                "SELECT col1 FROM table1",
                "INSERT INTO table2 VALUES (1, 'test')",
                "SELECT col1, COUNT(*) FROM table3 GROUP BY col1",
                "UPDATE table4 SET status = 'done'"
            ]
        },
        {
            'error': 'ERROR: function "UNKNOWN_FUNC" does not exist\nPosition: 150',
            'statements': [
                "SELECT col1, col2 FROM table1",
                "SELECT UNKNOWN_FUNC(col1) FROM table2",
                "INSERT INTO table3 VALUES (1, 2, 3)",
                "CREATE FUNCTION test_func() RETURNS INT"
            ]
        },
        {
            'error': 'undefined variable "missing_var" at position 75',
            'statements': [
                "var result = calculate(x, y);",
                "function test() { return missing_var + 10; }",
                "let data = fetch_data();",
                "const value = process(missing_var);"
            ]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test case {i}: {test_case['error'][:50]}...")
        
        for j, statement in enumerate(test_case['statements'], 1):
            relevance = resolver._calculate_content_relevance(
                test_case['error'], statement
            )
            print(f"   Statement {j}: {relevance:.3f} | {statement[:40]}...")
        
        # Find best match
        best_idx = max(range(len(test_case['statements'])), 
                      key=lambda i: resolver._calculate_content_relevance(
                          test_case['error'], test_case['statements'][i]
                      ))
        print(f"   🎯 Best match: Statement {best_idx + 1}")
    
    print("✅ Generic content relevance tests completed")

def test_generic_complexity_calculation():
    """Test generic complexity calculation"""
    print("\n🧪 Testing Generic Complexity Calculation...")
    
    mapper = AdvancedPositionMapper()
    resolver = SmartStatementResolver(mapper)
    
    # Test with statements of varying complexity
    test_statements = [
        "SELECT col1 FROM table1",  # Simple
        "SELECT col1, col2 FROM table1 WHERE condition = 'value'",  # Medium
        """SELECT t1.col1, t2.col2, COUNT(*) 
           FROM table1 t1 
           INNER JOIN table2 t2 ON t1.id = t2.ref_id 
           WHERE t1.status IN ('active', 'pending') 
           GROUP BY t1.col1, t2.col2 
           HAVING COUNT(*) > 5""",  # Complex
        "def complex_function(a, b, c):\n    if (a > b) and (c < (a + b)):\n        return ((a * b) + c) / (a - b)\n    else:\n        return None",  # Non-SQL complex
        "x = 1",  # Very simple
        """CREATE PROCEDURE complex_proc(@param1 INT, @param2 VARCHAR(50))
           AS
           BEGIN
               DECLARE @temp_var INT;
               SET @temp_var = (SELECT COUNT(*) FROM table1 WHERE col1 = @param1);
               IF @temp_var > 0
               BEGIN
                   INSERT INTO table2 (col1, col2) 
                   SELECT col1, @param2 FROM table1 WHERE col1 = @param1;
               END
           END"""  # Very complex
    ]
    
    print("\n📊 Complexity scores (0.0 = simple, 1.0 = very complex):")
    for i, statement in enumerate(test_statements, 1):
        complexity = resolver._calculate_statement_complexity(statement)
        print(f"   {i}. Score: {complexity:.3f} | {statement[:50]}...")
    
    print("✅ Generic complexity calculation tests completed")

def test_cross_language_compatibility():
    """Test that the approach works with non-SQL languages"""
    print("\n🧪 Testing Cross-Language Compatibility...")
    
    # Test with different programming languages
    test_cases = [
        {
            'language': 'Python',
            'error': 'NameError: name "undefined_var" is not defined',
            'code': '''def calculate_result(x, y):
    result = x + y + undefined_var
    return result * 2'''
        },
        {
            'language': 'JavaScript',
            'error': 'ReferenceError: missing_function is not defined',
            'code': '''function processData(data) {
    const processed = data.map(item => missing_function(item));
    return processed.filter(item => item !== null);
}'''
        },
        {
            'language': 'Java',
            'error': 'cannot find symbol: variable undeclaredVar',
            'code': '''public class TestClass {
    public int calculate(int a, int b) {
        return a + b + undeclaredVar;
    }
}'''
        }
    ]
    
    mapper = AdvancedPositionMapper()
    
    for test_case in test_cases:
        print(f"\n📝 Testing {test_case['language']}:")
        
        # Split into "statements" (lines in this case)
        lines = [line.strip() for line in test_case['code'].split('\n') if line.strip()]
        statements, position_mapper = mapper.split_with_comprehensive_mapping(test_case['code'])
        
        print(f"   📊 Split into {len(statements)} statements")
        
        # Test error extraction
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(test_case['error'])
        print(f"   📍 Extracted position info: {position_info}")
        
        # Test content relevance
        resolver = SmartStatementResolver(position_mapper)
        for i, statement in enumerate(statements, 1):
            relevance = resolver._calculate_content_relevance(
                test_case['error'], statement
            )
            if relevance > 0.1:  # Only show relevant matches
                print(f"   🎯 Statement {i} relevance: {relevance:.3f} | {statement[:40]}...")
    
    print("✅ Cross-language compatibility tests completed")

def main():
    """Run all generic approach tests"""
    print("🚀 Starting Generic Approach Tests (No Hardcoded Keywords)...\n")
    
    try:
        test_generic_content_hash()
        test_generic_content_relevance()
        test_generic_complexity_calculation()
        test_cross_language_compatibility()
        
        print("\n🎉 All generic approach tests completed successfully!")
        print("\n📋 Summary of Generic Features:")
        print("   ✅ No hardcoded SQL keywords")
        print("   ✅ Structural pattern recognition")
        print("   ✅ Statistical similarity measures")
        print("   ✅ Cross-language compatibility")
        print("   ✅ Token-based relevance calculation")
        print("   ✅ Generic complexity assessment")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
