#!/usr/bin/env python3
"""
Test script for the new position mapping functionality
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.error_position_mapper import (
    UniversalErrorExtractor,
    AdvancedPositionMapper,
    SmartStatementResolver
)

def test_error_extraction():
    """Test the universal error extractor"""
    print("🧪 Testing Universal Error Extractor...")
    
    extractor = UniversalErrorExtractor()
    
    # Test cases
    test_cases = [
        'syntax error at or near "GROUP"\nLINE 139:',
        'ERROR: syntax error at or near "TO"\n  Position: 17013',
        'Error position: line: 447 pos: 17012',
        'syntax error at position 15420',
        'error at line 184'
    ]
    
    for i, error_msg in enumerate(test_cases, 1):
        print(f"\n📝 Test case {i}: {error_msg}")
        position_info = extractor.extract_position_info(error_msg)
        print(f"   📊 Extracted: {position_info}")
    
    print("✅ Error extraction tests completed")

def test_position_mapping():
    """Test the position mapping functionality"""
    print("\n🧪 Testing Position Mapping...")
    
    # Sample SQL code
    sample_sql = """SET search_path TO LAB;

CREATE OR REPLACE PROCEDURE lab.P_TEST ()
LANGUAGE plpgsql
AS $BODY$
BEGIN
    SELECT COUNT(*) FROM table1;
    
    INSERT INTO table2 (col1, col2)
    SELECT col1, col2 
    FROM table3
    WHERE condition = 'value'
    GROUP BY col1, col2;
    
    UPDATE table4 
    SET status = 'updated'
    WHERE id > 100;
END;
$BODY$;"""
    
    # Create position mapper
    mapper = AdvancedPositionMapper()
    statements, position_mapper = mapper.split_with_comprehensive_mapping(sample_sql)
    
    print(f"📊 Split into {len(statements)} statements")
    
    for i, stmt in enumerate(statements, 1):
        print(f"\n📝 Statement {i}:")
        print(f"   {stmt[:100]}..." if len(stmt) > 100 else f"   {stmt}")
        
        if i in position_mapper.statement_ranges:
            start_pos, end_pos, start_line, end_line = position_mapper.statement_ranges[i]
            print(f"   📍 Position: chars {start_pos}-{end_pos}, lines {start_line}-{end_line}")
    
    print("✅ Position mapping tests completed")

def test_smart_resolution():
    """Test the smart statement resolver"""
    print("\n🧪 Testing Smart Statement Resolver...")
    
    # Sample SQL and error
    sample_sql = """SELECT col1, col2 FROM table1;
INSERT INTO table2 VALUES (1, 'test');
SELECT col1, COUNT(*) FROM table3 GROUP BY col1;
UPDATE table4 SET status = 'done';"""
    
    error_message = 'syntax error at or near "GROUP"\nLINE 3:'
    
    # Create mapper and resolver
    mapper = AdvancedPositionMapper()
    statements, position_mapper = mapper.split_with_comprehensive_mapping(sample_sql)
    
    resolver = SmartStatementResolver(position_mapper)
    
    # Test resolution
    statement_num, confidence, method = resolver.resolve_with_context_ranking(
        error_message, statements, iteration_count=1
    )
    
    print(f"📊 Resolved to statement #{statement_num} with confidence {confidence:.2f} using {method}")
    
    if statement_num:
        print(f"📝 Statement: {statements[statement_num-1]}")
    
    print("✅ Smart resolution tests completed")

def main():
    """Run all tests"""
    print("🚀 Starting Position Mapping Tests...\n")
    
    try:
        test_error_extraction()
        test_position_mapping()
        test_smart_resolution()
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
