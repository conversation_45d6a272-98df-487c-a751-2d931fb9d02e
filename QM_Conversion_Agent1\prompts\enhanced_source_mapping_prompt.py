"""
Enhanced prompts for source mapping with two-phase approach.
"""
from typing import List, <PERSON><PERSON>

def create_error_statement_identification_prompt(source_statements: List[str], target_error_statement: str, target_error_number: int, previous_feedback: str = None) -> str:
    """
    Phase 1: Identify the source statement that matches the target error statement.
    """
    source_text = "\n".join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(source_statements)])

    # Add feedback section if provided
    feedback_section = ""
    if previous_feedback:
        feedback_section = f"""
PREVIOUS MAPPING FEEDBACK:
The previous source mapping was rejected with this feedback:
{previous_feedback}

Please address these specific mapping issues:
- Review the functional equivalence more carefully
- Consider different Oracle-PostgreSQL syntax patterns
- Ensure logical flow alignment between source and target
- Focus on business outcome rather than syntax similarity
- Provide more detailed reasoning for your mapping choice

"""

    return f"""You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems. Your task is to identify the source Oracle statement that achieves the SAME BUSINESS OUTCOME as the target PostgreSQL error statement.

{feedback_section}

TARGET ERROR STATEMENT (#{target_error_number}):
{target_error_statement.strip()}

SOURCE STATEMENTS:
{source_text}

BUSINESS OUTCOME MAPPING APPROACH:
Focus on functional equivalence and business purpose rather than syntax similarity:

STEP 1: BUSINESS PURPOSE ANALYSIS
- What business purpose does the target error statement serve?
- Which source statement serves the same business purpose?
- Focus on what the statement accomplishes, not how it's written

STEP 2: FUNCTIONAL EQUIVALENCE ASSESSMENT
- Which source statement produces equivalent results to the target statement?
- Consider that different syntax can achieve the same business outcome
- Look for equivalent functional impact rather than syntax matching
- Recognize that some target statements may be target database-specific constructs with no source database equivalent

STRUCTURAL EQUIVALENCE ANALYSIS:
Your primary task is to find structural equivalence between target and source statements.

STEP 1: IDENTIFY STATEMENT STRUCTURE
- What is the structural type of the target error statement?
- Is it a control flow statement, data operation, declaration, or procedural construct?
- What is the exact statement pattern and syntax structure?
- Focus on the statement's grammatical structure, not its intended outcome

STEP 2: STRUCTURAL EQUIVALENCE MATCHING
- Find the source statement that has the same structural pattern as the target statement
- Match statement types to statement types (control flow to control flow, data operations to data operations)
- Focus on grammatical structure and statement construction patterns
- Look for the same statement syntax pattern, even if specific names/labels differ
- Prioritize structural similarity over functional outcome
- Match the statement's construction pattern, not what it accomplishes

**UNIVERSAL MAPPING PRINCIPLE: STRUCTURAL EQUIVALENCE**
- **Match statement types to statement types**: Control statements map to control statements
- **Match operations to operations**: Data operations map to data operations
- **Match constructs to constructs**: Procedural constructs map to procedural constructs
- **Focus on statement structure**: What type of database construct is this statement?
- **Avoid cross-type mapping**: Don't map control flow to data operations or vice versa

STEP 3: TARGET DATABASE-SPECIFIC DETECTION (ONLY IF NO EQUIVALENT EXISTS)
- Only consider a statement target database-specific if NO source statement serves the same purpose
- Use your database expertise to determine if this represents a feature that truly doesn't exist in the source database
- If genuinely target database-specific with no source equivalent, return source_statement_number: 0

TASK:
Find the ONE source statement that is STRUCTURALLY EQUIVALENT to the target error statement based on statement pattern and syntax structure.
Match the statement construction type, not the business outcome.
Only return 0 if the target statement represents a syntax pattern that genuinely has no structural equivalent in the source database.

ANALYSIS CRITERIA:
- Any database operation type including but not limited to:
  * Data Manipulation: INSERT, UPDATE, DELETE, SELECT, MERGE, UPSERT
  * Data Definition: CREATE, ALTER, DROP, TRUNCATE, RENAME
  * Transaction Control: COMMIT, ROLLBACK, SAVEPOINT, BEGIN
  * Procedural Operations: DECLARE, SET, CALL, EXECUTE, RETURN
  * Control Flow: IF/ELSE, CASE/WHEN, LOOP, WHILE, FOR, EXCEPTION handling
  * Cursor Operations: OPEN, FETCH, CLOSE cursor statements
  * Aggregate Operations: GROUP BY, HAVING, window functions, analytical functions
  * Join Operations: INNER/OUTER/CROSS joins, subqueries, CTEs
  * Index Operations: CREATE/DROP INDEX, hints, optimization directives
  * Security Operations: GRANT, REVOKE, user/role management
  * System Operations: sequence generation, trigger definitions, view creation
- Same target objects (tables, views, procedures, functions, sequences, etc.)
- Similar column/field operations and business logic
- Equivalent conditional logic (WHERE, HAVING, CASE conditions accounting for syntax differences)
- Same functional purpose and business outcome in the database migration
- Equivalent data flow and processing patterns
- Similar error handling and exception management approaches

SYNTAX DIFFERENCES TO CONSIDER:
- Database-specific date/time functions (e.g., SYSDATE vs current_timestamp vs NOW() vs GETDATE())
- XML/JSON processing functions and their cross-database equivalents
- String manipulation functions (SUBSTR vs SUBSTRING, CONCAT vs ||, etc.)
- Mathematical and statistical functions with different names/syntax
- Schema prefixes, object naming conventions, and case sensitivity rules
- Function call syntax differences between database dialects (parameter order, optional parameters)
- Data type handling variations (NUMBER vs NUMERIC vs INT, VARCHAR2 vs VARCHAR, etc.)
- Sequence generation methods (NEXTVAL vs AUTO_INCREMENT vs IDENTITY)
- Exception handling syntax (EXCEPTION WHEN vs TRY/CATCH vs IF ERROR)
- Cursor syntax and lifecycle management differences
- Transaction isolation and locking mechanism variations
- Stored procedure/function declaration and parameter syntax
- Trigger syntax and event handling differences
- Index creation syntax and optimization hint variations
- DO NOT assume specific database types - focus on functional equivalence and business logic preservation

OUTPUT FORMAT (JSON):
{{
  "source_statement_number": <integer_or_0_if_target_database_specific>,
  "confidence_score": <float between 0.0 and 1.0>,
  "reasoning": "<detailed analysis including: 1) Target database-specific detection (if applicable), 2) Specific functional similarities between source and target statements (if mappable), 3) Explanation of database operation equivalence, 4) Analysis of business logic preservation, 5) Identification of syntax transformations applied, 6) Assessment of data flow and processing patterns, 7) Detailed justification for the mapping decision or target database-specific determination>"
}}"""

def create_sequential_mapping_prompt(source_statements: List[str], target_context: List[Tuple[int, str]], identified_error_source: int, actual_error_target_number: int = None) -> str:
    """
    Phase 2: Create SEQUENTIAL mapping maintaining source statement order.
    """
    source_text = "\n".join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(source_statements)])
    target_context_text = "\n".join([f"{num}. {stmt.strip()}" for num, stmt in target_context])

    # Get target numbers and create dynamic context analysis
    target_numbers = [num for num, _ in target_context]

    # Determine error statement dynamically
    if actual_error_target_number:
        error_target_number = actual_error_target_number
    else:
        # Fallback logic if error number not provided
        error_target_number = target_numbers[0] if target_numbers else 0

    # Calculate sequential source statement numbers
    before_source = identified_error_source - 1 if identified_error_source > 1 else 0
    after_source = identified_error_source + 1 if identified_error_source < len(source_statements) else 0

    return f"""You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems. You have already identified that source Oracle statement #{identified_error_source} achieves the same business outcome as the target PostgreSQL error statement.

CRITICAL REQUIREMENT: MAINTAIN SEQUENTIAL SOURCE STATEMENT ORDER
You MUST create sequential mappings that preserve the logical flow of source statements.

SEQUENTIAL MAPPING RULE:
- Error statement: Target #{error_target_number} → Source #{identified_error_source}
- Before statement: Target statement before #{error_target_number} → Source #{before_source} (if exists)
- After statement: Target statement after #{error_target_number} → Source #{after_source} (if exists)

TARGET CONTEXT:
{target_context_text}

SOURCE STATEMENTS:
{source_text}

IDENTIFIED ERROR MAPPING:
- Target error statement #{error_target_number} maps to Source #{identified_error_source}

SEQUENTIAL MAPPING STRATEGY:
1. **Error Statement**: Target #{error_target_number} → Source #{identified_error_source}
2. **Before Statement**: Find target statement immediately before #{error_target_number} → Source #{before_source}
3. **After Statement**: Find target statement immediately after #{error_target_number} → Source #{after_source}

STEP 1: IDENTIFY TARGET STATEMENT POSITIONS
Analyze the target context to determine:
- Which target statement comes BEFORE #{error_target_number}
- Which target statement comes AFTER #{error_target_number}
- The error statement itself (#{error_target_number})

STEP 2: CREATE SEQUENTIAL MAPPINGS
Map target statements to their sequential source counterparts:
- Before error: Target statement before #{error_target_number} → Source #{before_source}
- Error statement: Target #{error_target_number} → Source #{identified_error_source}
- After error: Target statement after #{error_target_number} → Source #{after_source}

STEP 3: HANDLE EDGE CASES
- If no before/after target statement exists, use 0 for target_statement_number
- If before_source or after_source is out of bounds, use 0 for source_statement_number
- Always maintain the three-statement structure (before_error, error_statement, after_error)

OUTPUT FORMAT (JSON):
{{
  "source_statements": [
    {{
      "target_statement_number": <target_number_before_{error_target_number}_or_0>,
      "source_statement_number": {before_source},
      "statement_type": "before_error"
    }},
    {{
      "target_statement_number": {error_target_number},
      "source_statement_number": {identified_error_source},
      "statement_type": "error_statement"
    }},
    {{
      "target_statement_number": <target_number_after_{error_target_number}_or_0>,
      "source_statement_number": {after_source},
      "statement_type": "after_error"
    }}
  ],
  "validation_notes": "<analysis including: 1) Sequential mapping verification, 2) Target statement position identification, 3) Source statement boundary validation, 4) Edge case handling, 5) Sequential order preservation rationale>"
}}"""


