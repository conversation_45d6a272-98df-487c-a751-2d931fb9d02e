import sys
import uuid
from typing import Dict, Any
from config import ConfigManager
from llm import (
    AzureOpenAILLM,
    OpenAILLM,
    AnthropicLLM,
    GroqLLM,
    GeminiLLM,
    OllamaLLM
)
from workflow import GraphBuilder
from dotenv import load_dotenv
load_dotenv()


def create_llm(provider: str, config_manager: ConfigManager) -> Any:
    """
    Create and initialize a Language Model instance based on the specified provider.

    This function supports multiple LLM providers including Azure OpenAI, OpenAI, Anthropic,
    Groq, Gemini, and Ollama. Each provider is initialized with the appropriate configuration
    settings from the config manager.

    Args:
        provider: The LLM provider name (azure_openai, openai, anthropic, groq, gemini, ollama)
        config_manager: Configuration manager containing provider-specific settings

    Returns:
        An initialized instance of the appropriate LLM class ready for use

    Raises:
        ValueError: If the provider is not supported or recognized
    """
    if provider == "azure_openai":
        return AzureOpenAILLM(config_manager)
    elif provider == "openai":
        return OpenAILLM(config_manager)
    elif provider == "anthropic":
        return AnthropicLLM(config_manager)
    elif provider == "groq":
        return GroqLLM(config_manager)
    elif provider == "gemini":
        return GeminiLLM(config_manager)
    elif provider == "ollama":
        return OllamaLLM(config_manager)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")


def setup_application() -> Any:
    """
    Set up the application with configuration and initialize the Language Model.

    This function handles the complete application setup process including loading
    configuration settings and initializing the appropriate LLM provider based
    on the configuration.

    Returns:
        An initialized LLM instance ready for database migration workflows

    Raises:
        Exception: If LLM initialization fails
    """
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        print(f"🔧 Attempting to initialize {llm_provider} LLM...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} client")
        return llm
    except Exception as e:
        print(f"❌ Error initializing {llm_provider}: {str(e)}")
        raise


def run_workflow(llm: Any, source_code: str, target_code: str, deployment_error: str) -> Dict[str, Any]:
    """
    Execute the complete Oracle to PostgreSQL database migration workflow.

    This function orchestrates the entire migration process including error analysis,
    source mapping, statement conversion, and validation using AI-driven techniques.
    The workflow handles iterative improvements and maintains detailed tracking.

    Args:
        llm: Initialized Language Model instance for AI-driven analysis
        source_code: Original Oracle database code to be migrated
        target_code: PostgreSQL target code with potential deployment errors
        deployment_error: Error message from PostgreSQL deployment attempt

    Returns:
        Dictionary containing workflow execution results and final state
    """
    graph_builder = GraphBuilder(llm)
    graph_builder.setup_graph()
    graph_builder.save_graph_image(graph_builder.graph)


    # Create a unique thread ID for this workflow execution
    thread_id = f"thread_{uuid.uuid4()}"
    print(f"🔗 Using thread ID: {thread_id}")

    result = graph_builder.invoke_graph({
        "source_code": source_code,
        "target_code": target_code,
        "deployment_error": deployment_error,
        "iteration_count": 1  # Initialize iteration count to 1
    }, thread_id=thread_id)
    return result


def main():
    try:
        # Sample data for testing - in a real application, these would be provided by the user
        source_code = """

  CREATE OR REPLACE  PROCEDURE "LAB"."P_ADDRESULTSFROMEQUIPMENT10T" 

IS
V_LABREPORTID LABREPORTS.Labreportid%TYPE;
COUNTER NUMBER;
--A VARCHAR2(50);
--j NUMBER:=0;
LV_PARAM VARCHAR2(50);

-- CURSORS DECLARATION
CURSOR RESULTS IS
    SELECT DISTINCT ER.TRA_LINK_SAMPLEREQ_ID,
    EL.MST_EQUIPMENT_ID,
    TRIM(EL.SAMPLEID) SAMPLEID
    FROM EI_TRA_RESHEADER ER,
    EI_TRA_LINK_SAMPLEREQ EL
    WHERE ER.PROCESSED_STATUS='1'
    AND ER.TRA_LINK_SAMPLEREQ_ID=EL.TRA_LINK_SAMPLEREQ_ID
    and rownum<600;


CURSOR RESULT1(EQSID VARCHAR2)
IS
    SELECT DISTINCT RTC.requesttestid FROM requesttests RTC
    WHERE RTC.SIN=LV_PARAM
    AND RTC.SAMPLECOLLECTED='Y'
    AND RTC.PREPARESAMPLE='Y'
    AND RTC.Sampleverify='N'
    AND RTC.TYPEOFPROCESSING in (323,322);


-- Variable daclaration for Result1 Cursor
TYPE lt_requesttestid IS TABLE OF REQUESTTESTS.REQUESTTESTID%TYPE INDEX BY BINARY_INTEGER;
lv_requesttestid lt_requesttestid;
-- Record Type Creation for Results Cusor
TYPE record_results IS RECORD
   (rv_tra_link_samplereq_id   EI_TRA_RESHEADER.TRA_LINK_SAMPLEREQ_ID%TYPE,
   rv_mst_equipment_id  EI_TRA_LINK_SAMPLEREQ.MST_EQUIPMENT_ID%TYPE,
   rv_sampleid  EI_TRA_LINK_SAMPLEREQ.SAMPLEID%TYPE
      );
TYPE tb_record_results IS TABLE OF record_results INDEX BY BINARY_INTEGER;
var_record_results tb_record_results;

-- Execution Block Begins

BEGIN
   OPEN RESULTS;
    FETCH RESULTS BULK COLLECT INTO var_record_results;
   -- FORALL I IN var_record_results.FIRST .. var_record_results.LAST
      -- Iterating The Collection Variable for Results Cursor Begins
      FOR REC_COUNT IN 1..var_record_results.count LOOP
       BEGIN
         LV_PARAM:=var_record_results(REC_COUNT).rv_sampleid;
        BEGIN
         OPEN RESULT1(LV_PARAM);
         FETCH RESULT1 BULK COLLECT  INTO lv_requesttestid ;
          IF(lv_requesttestid.COUNT =0)THEN

           UPDATE EI_TRA_RESHEADER
              SET PROCESSED_STATUS = '3'
            WHERE TRA_LINK_SAMPLEREQ_ID=var_record_results(REC_COUNT).rv_tra_link_samplereq_id;

          ELSE
          --  dbms_output.put_line('inner cursor count'|| lv_requesttestid.COUNT);
          FOR RESULT_COUNT IN 1..lv_requesttestid.COUNT LOOP

            BEGIN
              SELECT L.LABREPORTID
              INTO V_LABREPORTID
              FROM LABREPORTS L
              WHERE L.REQUESTTESTID = lv_requesttestid(RESULT_COUNT);

              UPDATE LABREPORTS LR
              SET LR.UPDATEDDATE = SYSDATE
              WHERE LR.LABREPORTID=V_LABREPORTID;

             EXCEPTION
              WHEN NO_DATA_FOUND THEN
              SELECT S_LABREPORTID.NEXTVAL INTO V_LABREPORTID FROM DUAL;
              INSERT INTO LABREPORTS(LABREPORTID
              ,REQUESTTESTID
              ,REPRINT
              ,DISPATCH
              ,NOOFORGINALS
              ,NOOFDUPLICATES
              ,DISPATCHON
              ,REPORTVERIFY
              ,REPORTPRINTDATE
              ,DUPLICATEREPORTPRINTDATE
              ,STATUS
              ,CREATEDBY
              ,CREATEDDATE
              ,UPDATEDBY
              ,UPDATEDDATE
              ,GROUPNAME)
              VALUES(V_LABREPORTID
              ,lv_requesttestid(RESULT_COUNT)
              ,NULL
              ,NULL
              ,NULL
              ,NULL
              ,NULL
              ,NULL
              ,NULL
              ,NULL
              ,1
              ,NULL
              ,SYSDATE
              ,NULL
              ,NULL
              ,NULL);


              END;


              UPDATE TESTREPORTS B
              SET B.UPDATEDDATE = SYSDATE
              WHERE LABREPORTID =V_LABREPORTID;


          IF SQL%ROWCOUNT =0 THEN
             INSERT INTO TESTREPORTS(PARAMETERID
              ,PARAMETERDETAILID
              ,RESULT
              ,REPORTSTATUS
              ,STATUS
              ,CREATEDBY
              ,CREATEDDATE
              ,COMMENTS
              ,UPDATEDBY
              ,UPDATEDDATE
              ,LABREPORTID
              ,TRA_LINK_SAMPLEREQ_ID
              ,NUMBEROFREPEATS)
              SELECT DISTINCT tp.parameterid,
              LLP.PARAMETERDETAILID,(case when abc.mst_equipment_id=36
              then eta.param_result_flag
              else ETA.EQP_PARAM_RESULT
              end) ,NULL
              ,1
              ,NULL
              ,SYSDATE
              ,NULL
              ,NULL
              ,NULL
              ,V_LABREPORTID
              ,var_record_results(REC_COUNT).rv_tra_link_samplereq_id
              ,null


              FROM requesttests RT
              INNER JOIN testmaster TM ON RT.testid=TM.serviceid
              INNER JOIN testparameter TP ON TM.serviceid= tp.testid
              INNER JOIN PARAMETERMASTERMAINDETAIL PM ON TP.PARAMETERID=PM.PARAMETERID
              INNER JOIN lab_link_param LLP ON llp.parameterdetailid= PM.parameterdetailid
              INNER JOIN ei_mst_link_param EI ON ei.mst_link_param_id= llp.mst_link_param_id
              inner join ei_tra_resheader ETA on ei.eqp_param_cd=ETA.EQP_PARAM_CD
              inner join ei_tra_link_samplereq abc on abc.tra_link_samplereq_id=eta.tra_link_samplereq_id
              WHERE (RT.requesttestid=lv_requesttestid(RESULT_COUNT))
              and trim(abc.sampleid)=trim(var_record_results(REC_COUNT).rv_sampleid)
              AND TM.STATUS =1
              AND EI.MST_EQUIPMENT_ID=var_record_results(REC_COUNT).rv_mst_equipment_id
              AND RT.TESTSTATUS =1
              AND ETA.REPEAT_FLAG='R'
              -- AND ETA.PROCESSED_STATUS=1
              HAVING MAX(eta.test_transmission_time)=MAX(eta.test_transmission_time)
              GROUP BY tp.parameterid,
              LLP.PARAMETERDETAILID,abc.mst_equipment_id,eta.param_result_flag,ETA.EQP_PARAM_RESULT;
              --=================================================================================
              IF SQL%ROWCOUNT=0 THEN

                 INSERT INTO TESTREPORTS(PARAMETERID
                  ,PARAMETERDETAILID
                  ,RESULT
                  ,REPORTSTATUS
                  ,STATUS
                  ,CREATEDBY
                  ,CREATEDDATE
                  ,COMMENTS
                  ,UPDATEDBY
                  ,UPDATEDDATE
                  ,LABREPORTID
                  ,TRA_LINK_SAMPLEREQ_ID
                  ,NUMBEROFREPEATS)
                  SELECT DISTINCT tp.parameterid,
                  LLP.PARAMETERDETAILID,(case when abc.mst_equipment_id=36
                  then eta.param_result_flag
                  else ETA.EQP_PARAM_RESULT
                  end),NULL
                  ,1
                  ,NULL
                  ,SYSDATE
                  ,NULL
                  ,NULL
                  ,NULL
                  ,V_LABREPORTID
                  ,var_record_results(REC_COUNT).rv_tra_link_samplereq_id
                  ,null



                  FROM requesttests RT
                  INNER JOIN testmaster TM ON RT.testid=TM.serviceid
                  INNER JOIN testparameter TP ON TM.serviceid= tp.testid
                  INNER JOIN PARAMETERMASTERMAINDETAIL PM ON TP.PARAMETERID=PM.PARAMETERID
                  INNER JOIN lab_link_param LLP ON llp.parameterdetailid= PM.parameterdetailid
                  INNER JOIN ei_mst_link_param EI ON ei.mst_link_param_id= llp.mst_link_param_id
                  inner join ei_tra_resheader ETA on ei.eqp_param_cd=ETA.EQP_PARAM_CD
                  inner join ei_tra_link_samplereq abc on abc.tra_link_samplereq_id=eta.tra_link_samplereq_id
                  WHERE (RT.requesttestid=lv_requesttestid(RESULT_COUNT) )
                  and trim(abc.sampleid)=trim(var_record_results(REC_COUNT).rv_sampleid)
                  AND TM.STATUS =1
                  AND EI.MST_EQUIPMENT_ID=var_record_results(REC_COUNT).rv_mst_equipment_id
                  AND RT.TESTSTATUS =1
                  AND ETA.REPEAT_FLAG='N'
                  --AND ETA.PROCESSED_STATUS=1
                  HAVING MAX(eta.test_transmission_time)=MAX(eta.test_transmission_time)
                  GROUP BY tp.parameterid,
                  LLP.PARAMETERDETAILID,abc.mst_equipment_id,eta.param_result_flag,ETA.EQP_PARAM_RESULT;
              END IF;


              UPDATE EI_TRA_RESHEADER
                 SET PROCESSED_STATUS = '2'
               WHERE TRA_LINK_SAMPLEREQ_ID = var_record_results(REC_COUNT).rv_tra_link_samplereq_id;


              SELECT COUNT(L.REQUESTTESTID)
                INTO COUNTER
                FROM TESTREPORTS T INNER JOIN LABREPORTS L ON T.LABREPORTID = L.LABREPORTID
               WHERE L.REQUESTTESTID = lv_requesttestid(RESULT_COUNT);

              IF (COUNTER<>0) THEN
                    UPDATE REQUESTTESTS T
                       SET T.SAMPLEVERIFY      = 'E',
                           T.PROCESSING        = 'Y',
                           T.Samplecollected   = 'Y',
                           t.preparesample     = 'Y',
                           t.updateddate       = sysdate,
                           t.sampleprocesstime = sysdate,
                           T.EQUIPMENTID       = var_record_results(REC_COUNT).rv_mst_equipment_id
                     WHERE T.REQUESTTESTID = lv_requesttestid(RESULT_COUNT);
              END IF;

              ------------------------------------------------------------------------------------

              --------------------------------------------------------------------------------

          END IF;

         END LOOP; -- Results1 Loop End
       END IF;  -- End If for Request Test id is null
          EXCEPTION
            WHEN NO_DATA_FOUND THEN
            UPDATE EI_TRA_RESHEADER
            SET PROCESSED_STATUS='3'
            WHERE TRA_LINK_SAMPLEREQ_ID=var_record_results(REC_COUNT).rv_tra_link_samplereq_id;
     END; -- Results1 Block End

     CLOSE RESULT1;
      EXCEPTION
        WHEN NO_DATA_FOUND THEN
        Dbms_Output.put_line(Sqlcode || Sqlerrm);
        UPDATE EI_TRA_RESHEADER
        SET PROCESSED_STATUS='3'
        WHERE TRA_LINK_SAMPLEREQ_ID=var_record_results(REC_COUNT).rv_tra_link_samplereq_id;

     END; -- Results Cursors End Block
    END LOOP; -- Results Cursor  Loop End

 Exception
  When Others Then
  Dbms_Output.Put_Line(Sqlcode || Sqlerrm);
END P_ADDRESULTSFROMEQUIPMENT10T;
        """

        target_code = """
SET search_path TO LAB;

CREATE OR REPLACE PROCEDURE lab.P_ADDRESULTSFROMEQUIPMENT10T ()
LANGUAGE plpgsql
SECURITY DEFINER
AS $BODY$
DECLARE
    V_LABREPORTID lab.LABREPORTS.Labreportid%TYPE;
    COUNTER numeric;
    --A VARCHAR2(50);
    --j NUMBER:=0;
    LV_PARAM varchar(50);
    -- CURSORS DECLARATION
    RESULTS CURSOR FOR SELECT DISTINCT
            ER.TRA_LINK_SAMPLEREQ_ID,
            EL.MST_EQUIPMENT_ID,
            TRIM(EL.SAMPLEID) SAMPLEID
        FROM
            lab.EI_TRA_RESHEADER ER,
            EI_TRA_LINK_SAMPLEREQ EL
        WHERE
            ER.PROCESSED_STATUS = '1'
            AND ER.TRA_LINK_SAMPLEREQ_ID = EL.TRA_LINK_SAMPLEREQ_ID
        LIMIT 599;
    RESULT1 CURSOR (EQSID varchar)
    FOR SELECT DISTINCT
            RTC.requesttestid
        FROM
            lab.requesttests RTC
        WHERE
            RTC.SIN = LV_PARAM
            AND RTC.SAMPLECOLLECTED = 'Y'
            AND RTC.PREPARESAMPLE = 'Y'
            AND RTC.Sampleverify = 'N'
            AND RTC.TYPEOFPROCESSING IN (323, 322);
    -- Variable daclaration for Result1 Cursor
    -- -- --TYPE lt_requesttestid is table of lab.REQUESTTESTS.REQUESTTESTID%TYPE INDEX BY integer;
    --lv_requesttestid lt_requesttestid
    lv_requesttestid varchar[];
    -- Record Type Creation for Results Cusor
    record_results record;
    rv_mst_equipment_id lab.EI_TRA_LINK_SAMPLEREQ.MST_EQUIPMENT_ID%TYPE;
    rv_tra_link_samplereq_id lab.EI_TRA_RESHEADER.TRA_LINK_SAMPLEREQ_ID%TYPE;
    rv_sampleid lab.EI_TRA_LINK_SAMPLEREQ.SAMPLEID%TYPE;
    -- ----TYPE tb_record_results is table of record_results INDEX BY integer;
    var_record_results varchar[];
    -- Execution Block Begins
    ora2pg_rowcount int := 0;
    REC_COUNT record;
    RESULT_COUNT record;
BEGIN
    SET search_path TO LAB;
    OPEN RESULTS;
    FETCH RESULTS INTO var_record_results;
    -- FORALL I IN var_record_results.FIRST .. var_record_results.LAST
    -- Iterating The Collection Variable for Results Cursor Begins
    FOR REC_COUNT IN 1..var_record_results.count LOOP
        BEGIN
            LV_PARAM := var_record_results[REC_COUNT].rv_sampleid;
            BEGIN
                OPEN RESULT1 (LV_PARAM);
                FETCH RESULT1 INTO lv_requesttestid;
                IF (lv_requesttestid.COUNT = 0) THEN
                    UPDATE
                        lab.EI_TRA_RESHEADER
                    SET
                        PROCESSED_STATUS = '3'
                    WHERE
                        TRA_LINK_SAMPLEREQ_ID = var_record_results[REC_COUNT].rv_tra_link_samplereq_id;
                ELSE
                    -- dbms_output.put_line('inner cursor count'|| lv_requesttestid.COUNT);
                    FOR RESULT_COUNT IN 1..lv_requesttestid.COUNT LOOP
                        BEGIN
                            SELECT
                                L.LABREPORTID INTO STRICT V_LABREPORTID
                            FROM
                                lab.LABREPORTS L
                            WHERE
                                L.REQUESTTESTID = lv_requesttestid[RESULT_COUNT];
                            UPDATE
                                lab.LABREPORTS LR
                            SET
                                UPDATEDDATE = current_timestamp(0)::timestamp
                            WHERE
                                LR.LABREPORTID = V_LABREPORTID;
                        EXCEPTION
                            WHEN NO_DATA_FOUND THEN
                                SELECT
                                    nextval('lab.S_LABREPORTID') INTO STRICT V_LABREPORTID;
                        INSERT INTO LABREPORTS (LABREPORTID, REQUESTTESTID, REPRINT, DISPATCH, NOOFORGINALS, NOOFDUPLICATES, DISPATCHON, REPORTVERIFY, REPORTPRINTDATE, DUPLICATEREPORTPRINTDATE, STATUS, CREATEDBY, CREATEDDATE, UPDATEDBY, UPDATEDDATE, GROUPNAME)
                            VALUES (V_LABREPORTID, lv_requesttestid[RESULT_COUNT], NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, current_timestamp(0)::timestamp, NULL, NULL, NULL);
                        END;
                        UPDATE
                            lab.TESTREPORTS B
                        SET
                            UPDATEDDATE = current_timestamp(0)::timestamp
                        WHERE
                            LABREPORTID = V_LABREPORTID;
                            GET DIAGNOSTICS ora2pg_rowcount = row_count;
                            IF ora2pg_rowcount = 0 THEN
                                INSERT INTO TESTREPORTS (PARAMETERID, PARAMETERDETAILID, RESULT, REPORTSTATUS, STATUS, CREATEDBY, CREATEDDATE, COMMENTS, UPDATEDBY, UPDATEDDATE, LABREPORTID, TRA_LINK_SAMPLEREQ_ID, NUMBEROFREPEATS)
                                SELECT DISTINCT
                                    tp.parameterid,
                                    LLP.PARAMETERDETAILID,
                                    (
                                        CASE WHEN abc.mst_equipment_id = 36 THEN
                                            eta.param_result_flag
                                        ELSE
                                            ETA.EQP_PARAM_RESULT
                                        END),
                                    NULL,
                                    1,
                                    NULL,
                                    current_timestamp(0)::timestamp,
                                    NULL,
                                    NULL,
                                    NULL,
                                    V_LABREPORTID,
                                    var_record_results[REC_COUNT].rv_tra_link_samplereq_id,
                                    NULL
                                FROM
                                    lab.requesttests RT
                                    INNER JOIN lab.testmaster TM ON RT.testid = TM.serviceid
                                    INNER JOIN lab.testparameter TP ON TM.serviceid = tp.testid
                                    INNER JOIN lab.PARAMETERMASTERMAINDETAIL PM ON TP.PARAMETERID = PM.PARAMETERID
                                    INNER JOIN lab.lab_link_param LLP ON llp.parameterdetailid = PM.parameterdetailid
                                    INNER JOIN lab.ei_mst_link_param EI ON ei.mst_link_param_id = llp.mst_link_param_id
                                    INNER JOIN lab.ei_tra_resheader ETA ON ei.eqp_param_cd = ETA.EQP_PARAM_CD
                                    INNER JOIN lab.ei_tra_link_samplereq abc ON abc.tra_link_samplereq_id = eta.tra_link_samplereq_id
                                WHERE (RT.requesttestid = lv_requesttestid[RESULT_COUNT])
                                    AND trim(abc.sampleid)::numeric = trim(var_record_results[REC_COUNT].rv_sampleid)
                                    AND TM.STATUS = 1
                                    AND EI.MST_EQUIPMENT_ID = var_record_results[REC_COUNT].rv_mst_equipment_id::numeric
                                    AND RT.TESTSTATUS = 1
                                    AND ETA.REPEAT_FLAG::numeric = 'R'
                                    -- AND ETA.PROCESSED_STATUS=1
                                HAVING
                                    MAX(eta.test_transmission_time) = MAX(eta.test_transmission_time)
                                GROUP BY
                                    tp.parameterid,
                                    LLP.PARAMETERDETAILID,
                                    abc.mst_equipment_id,
                                    eta.param_result_flag,
                                    ETA.EQP_PARAM_RESULT;
                                    --=================================================================================
                                    GET DIAGNOSTICS ora2pg_rowcount = row_count;
                                    IF ora2pg_rowcount = 0 THEN
                                        INSERT INTO TESTREPORTS (PARAMETERID, PARAMETERDETAILID, RESULT, REPORTSTATUS, STATUS, CREATEDBY, CREATEDDATE, COMMENTS, UPDATEDBY, UPDATEDDATE, LABREPORTID, TRA_LINK_SAMPLEREQ_ID, NUMBEROFREPEATS)
                                        SELECT DISTINCT
                                            tp.parameterid,
                                            LLP.PARAMETERDETAILID,
                                            (
                                                CASE WHEN abc.mst_equipment_id = 36 THEN
                                                    eta.param_result_flag
                                                ELSE
                                                    ETA.EQP_PARAM_RESULT
                                                END),
                                            NULL,
                                            1,
                                            NULL,
                                            current_timestamp(0)::timestamp,
                                            NULL,
                                            NULL,
                                            NULL,
                                            V_LABREPORTID,
                                            var_record_results[REC_COUNT].rv_tra_link_samplereq_id,
                                            NULL
                                        FROM
                                            lab.requesttests RT
                                            INNER JOIN lab.testmaster TM ON RT.testid = TM.serviceid
                                            INNER JOIN lab.testparameter TP ON TM.serviceid = tp.testid
                                            INNER JOIN lab.PARAMETERMASTERMAINDETAIL PM ON TP.PARAMETERID = PM.PARAMETERID
                                            INNER JOIN lab.lab_link_param LLP ON llp.parameterdetailid = PM.parameterdetailid
                                            INNER JOIN lab.ei_mst_link_param EI ON ei.mst_link_param_id = llp.mst_link_param_id
                                            INNER JOIN lab.ei_tra_resheader ETA ON ei.eqp_param_cd = ETA.EQP_PARAM_CD
                                            INNER JOIN lab.ei_tra_link_samplereq abc ON abc.tra_link_samplereq_id = eta.tra_link_samplereq_id
                                        WHERE (RT.requesttestid = lv_requesttestid[RESULT_COUNT])
                                            AND trim(abc.sampleid)::numeric = trim(var_record_results[REC_COUNT].rv_sampleid)
                                            AND TM.STATUS = 1
                                            AND EI.MST_EQUIPMENT_ID = var_record_results[REC_COUNT].rv_mst_equipment_id::numeric
                                            AND RT.TESTSTATUS = 1
                                            AND ETA.REPEAT_FLAG::numeric = 'N'
                                            --AND ETA.PROCESSED_STATUS=1
                                        HAVING
                                            MAX(eta.test_transmission_time) = MAX(eta.test_transmission_time)
                                        GROUP BY
                                            tp.parameterid,
                                            LLP.PARAMETERDETAILID,
                                            abc.mst_equipment_id,
                                            eta.param_result_flag,
                                            ETA.EQP_PARAM_RESULT;
                                        END IF;
                                        UPDATE
                                            lab.EI_TRA_RESHEADER
                                        SET
                                            PROCESSED_STATUS = '2'
                                        WHERE
                                            TRA_LINK_SAMPLEREQ_ID = var_record_results[REC_COUNT].rv_tra_link_samplereq_id;
                                            SELECT
                                                COUNT(L.REQUESTTESTID) INTO STRICT COUNTER
                                            FROM
                                                lab.TESTREPORTS T
                                                INNER JOIN lab.LABREPORTS L ON T.LABREPORTID = L.LABREPORTID
                                            WHERE
                                                L.REQUESTTESTID = lv_requesttestid[RESULT_COUNT];
                                                IF (COUNTER <> 0) THEN
                                                    UPDATE
                                                        lab.REQUESTTESTS T
                                                    SET
                                                        SAMPLEVERIFY = 'E',
                                                        PROCESSING = 'Y',
                                                        Samplecollected = 'Y',
                                                        preparesample = 'Y',
                                                        updateddate = current_timestamp(0)::timestamp,
                                                        sampleprocesstime = current_timestamp(0)::timestamp,
                                                        EQUIPMENTID = var_record_results[REC_COUNT].rv_mst_equipment_id
                                                    WHERE
                                                        T.REQUESTTESTID = lv_requesttestid[RESULT_COUNT];
                                                    END IF;
                                                    --
                                                    --
                                END IF;
                    END LOOP;
                    -- Results1 Loop End
                END IF;
                -- End If for Request Test id is null
            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    UPDATE
                        lab.EI_TRA_RESHEADER
                    SET
                        PROCESSED_STATUS = '3'
                    WHERE
                        TRA_LINK_SAMPLEREQ_ID = var_record_results[REC_COUNT].rv_tra_link_samplereq_id;
            END;
            -- Results1 Block End
            CLOSE RESULT1;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                RAISE NOTICE '% %', sqlstate, sqlerrm;
                UPDATE
                    lab.EI_TRA_RESHEADER
                SET
                    PROCESSED_STATUS = '3'
                WHERE
                    TRA_LINK_SAMPLEREQ_ID = var_record_results[REC_COUNT].rv_tra_link_samplereq_id;
        END;
        -- Results Cursors End Block
END LOOP;
-- Results Cursor Loop End
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '% %', sqlstate, sqlerrm;
END;

$BODY$;
               """

        deployment_error = """
    syntax error at or near "GROUP"
LINE 139:                                 GROUP BY

        """

        llm = setup_application()
        run_workflow(llm, source_code, target_code, deployment_error)

        print("\n🎉 Execution completed successfully!")

    except ValueError as e:
        print(f"\nConfiguration Error: {str(e)}")
        print("\nPlease check your configuration and try again.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected Error: {str(e)}")
        print("\nPlease report this issue with the error details above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
